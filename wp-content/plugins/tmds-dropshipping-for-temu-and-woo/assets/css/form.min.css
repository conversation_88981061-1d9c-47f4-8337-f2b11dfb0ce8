/*!
 * # Semantic UI 2.2.12 - Form
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */
@-webkit-keyframes form-spin{0%{-webkit-transform:rotate(0);transform:rotate(0)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes form-spin{0%{-webkit-transform:rotate(0);transform:rotate(0)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}.vi-ui.form{position:relative;max-width:100%;font-size:1rem}.vi-ui.form>p{margin:1em 0}.vi-ui.form .field{clear:both;margin:0 0 1em}.vi-ui.form .field:last-child,.vi-ui.form .fields:last-child .field{margin-bottom:0}.vi-ui.form .fields .field{clear:both;margin:0}.vi-ui.form .field>label{display:block;margin:0 0 .28571429rem;color:rgba(0,0,0,.87);font-size:.92857143em;font-weight:700;text-transform:none}.vi-ui.form input:not([type]),.vi-ui.form input[type=date],.vi-ui.form input[type=datetime-local],.vi-ui.form input[type=email],.vi-ui.form input[type=file],.vi-ui.form input[type=number],.vi-ui.form input[type=password],.vi-ui.form input[type=search],.vi-ui.form input[type=tel],.vi-ui.form input[type=text],.vi-ui.form input[type=time],.vi-ui.form input[type=url],.vi-ui.form textarea{width:100%;vertical-align:top}.vi-ui.form ::-webkit-datetime-edit,.vi-ui.form ::-webkit-inner-spin-button{height:1.21428571em}.vi-ui.form input:not([type]),.vi-ui.form input[type=date],.vi-ui.form input[type=datetime-local],.vi-ui.form input[type=email],.vi-ui.form input[type=file],.vi-ui.form input[type=number],.vi-ui.form input[type=password],.vi-ui.form input[type=search],.vi-ui.form input[type=tel],.vi-ui.form input[type=text],.vi-ui.form input[type=time],.vi-ui.form input[type=url]{font-family:Lato,'Helvetica Neue',Arial,Helvetica,sans-serif;margin:0;outline:0;-webkit-appearance:none;tap-highlight-color:transparent;line-height:1.21428571em;padding:.67857143em 1em;font-size:1em;background:#fff;border:1px solid rgba(34,36,38,.15);color:rgba(0,0,0,.87);border-radius:.28571429rem;box-shadow:0 0 0 0 transparent inset;-webkit-transition:color .1s ease,border-color .1s ease;transition:color .1s ease,border-color .1s ease;height:auto!important}.vi-ui.form textarea{margin:0;-webkit-appearance:none;tap-highlight-color:transparent;padding:.78571429em 1em;background:#fff;border:1px solid rgba(34,36,38,.15);outline:0;color:rgba(0,0,0,.87);border-radius:.28571429rem;box-shadow:0 0 0 0 transparent inset;-webkit-transition:color .1s ease,border-color .1s ease;transition:color .1s ease,border-color .1s ease;font-size:1em;line-height:1.2857;resize:vertical}.vi-ui.form textarea:not([rows]){height:12em;min-height:8em;max-height:24em}.vi-ui.form input[type=checkbox],.vi-ui.form textarea{vertical-align:top}.vi-ui.form select{height:38px;display:block;width:100%;background:#fff;border:1px solid rgba(34,36,38,.15);border-radius:.28571429rem;box-shadow:0 0 0 0 transparent inset;padding:.62em 1em;color:rgba(0,0,0,.87);-webkit-transition:color .1s ease,border-color .1s ease;transition:color .1s ease,border-color .1s ease}.vi-ui.form .field>.selection.dropdown>.dropdown.icon{float:right}.vi-ui.form .field .vi-ui.input input,.vi-ui.form .fields .field .vi-ui.input input,.vi-ui.form .inline.field>.selection.dropdown,.vi-ui.form .inline.fields .field>.selection.dropdown,.vi-ui.form input.attached{width:auto}.vi-ui.form .inline.field>.selection.dropdown>.dropdown.icon,.vi-ui.form .inline.fields .field>.selection.dropdown>.dropdown.icon{float:none}.vi-ui.form .field .vi-ui.input,.vi-ui.form .field>.selection.dropdown,.vi-ui.form .fields .field .vi-ui.input,.vi-ui.form .wide.field .vi-ui.input{width:100%}.vi-ui.form .inline.field:not(.wide) .vi-ui.input,.vi-ui.form .inline.fields .field:not(.wide) .vi-ui.input{width:auto;vertical-align:middle}.vi-ui.form .eight.fields .vi-ui.input input,.vi-ui.form .five.fields .vi-ui.input input,.vi-ui.form .four.fields .vi-ui.input input,.vi-ui.form .nine.fields .vi-ui.input input,.vi-ui.form .seven.fields .vi-ui.input input,.vi-ui.form .six.fields .vi-ui.input input,.vi-ui.form .ten.fields .vi-ui.input input,.vi-ui.form .three.fields .vi-ui.input input,.vi-ui.form .two.fields .vi-ui.input input,.vi-ui.form .wide.field .vi-ui.input input{-webkit-box-flex:1;-ms-flex:1 0 auto;flex:1 0 auto;width:0}.vi-ui.form .error.message,.vi-ui.form .success.message,.vi-ui.form .warning.message{display:none}.vi-ui.form .message:first-child{margin-top:0}.vi-ui.form .field .prompt.label{white-space:normal;background:#fff!important;border:1px solid #e0b4b4!important;color:#9f3a38!important}.vi-ui.form .inline.field .prompt,.vi-ui.form .inline.fields .field .prompt{vertical-align:top;margin:-.25em 0 -.5em .5em}.vi-ui.form .inline.field .prompt:before,.vi-ui.form .inline.fields .field .prompt:before{border-width:0 0 1px 1px;bottom:auto;right:auto;top:50%;left:0}.vi-ui.form .field.field input:-webkit-autofill{box-shadow:0 0 0 100px ivory inset!important;border-color:#e5dfa1!important}.vi-ui.form .field.field input:-webkit-autofill:focus{box-shadow:0 0 0 100px ivory inset!important;border-color:#d5c315!important}.vi-ui.form .error.error input:-webkit-autofill{box-shadow:0 0 0 100px #fffaf0 inset!important;border-color:#e0b4b4!important}.vi-ui.form ::-webkit-input-placeholder{color:rgba(191,191,191,.87)}.vi-ui.form :-ms-input-placeholder{color:rgba(191,191,191,.87)}.vi-ui.form ::-moz-placeholder{color:rgba(191,191,191,.87)}.vi-ui.form :focus::-webkit-input-placeholder{color:rgba(115,115,115,.87)}.vi-ui.form :focus:-ms-input-placeholder{color:rgba(115,115,115,.87)}.vi-ui.form :focus::-moz-placeholder{color:rgba(115,115,115,.87)}.vi-ui.form .error ::-webkit-input-placeholder{color:#e7bdbc}.vi-ui.form .error :-ms-input-placeholder{color:#e7bdbc!important}.vi-ui.form .error ::-moz-placeholder{color:#e7bdbc}.vi-ui.form .error :focus::-webkit-input-placeholder{color:#da9796}.vi-ui.form .error :focus:-ms-input-placeholder{color:#da9796!important}.vi-ui.form .error :focus::-moz-placeholder{color:#da9796}.vi-ui.form input:not([type]):focus,.vi-ui.form input[type=date]:focus,.vi-ui.form input[type=datetime-local]:focus,.vi-ui.form input[type=email]:focus,.vi-ui.form input[type=file]:focus,.vi-ui.form input[type=number]:focus,.vi-ui.form input[type=password]:focus,.vi-ui.form input[type=search]:focus,.vi-ui.form input[type=tel]:focus,.vi-ui.form input[type=text]:focus,.vi-ui.form input[type=time]:focus,.vi-ui.form input[type=url]:focus{color:rgba(0,0,0,.95);border-color:#85b7d9;border-radius:.28571429rem;background:#fff;box-shadow:0 0 0 0 rgba(34,36,38,.35) inset}.vi-ui.form textarea:focus{color:rgba(0,0,0,.95);border-color:#85b7d9;border-radius:.28571429rem;background:#fff;box-shadow:0 0 0 0 rgba(34,36,38,.35) inset;-webkit-appearance:none}.vi-ui.form.error .error.message:not(:empty),.vi-ui.form.success .success.message:not(:empty),.vi-ui.form.warning .warning.message:not(:empty){display:block}.vi-ui.form.success .compact.success.message:not(:empty){display:inline-block}.vi-ui.form.success .icon.success.message:not(:empty){display:-webkit-box;display:-ms-flexbox;display:flex}.vi-ui.form.warning .compact.warning.message:not(:empty){display:inline-block}.vi-ui.form.warning .icon.warning.message:not(:empty){display:-webkit-box;display:-ms-flexbox;display:flex}.vi-ui.form.error .compact.error.message:not(:empty){display:inline-block}.vi-ui.form.error .icon.error.message:not(:empty){display:-webkit-box;display:-ms-flexbox;display:flex}.vi-ui.form .field.error .checkbox .box:after,.vi-ui.form .field.error .checkbox label:after,.vi-ui.form .field.error .input,.vi-ui.form .field.error label,.vi-ui.form .fields.error .field .checkbox .box:after,.vi-ui.form .fields.error .field .checkbox label:after,.vi-ui.form .fields.error .field .input,.vi-ui.form .fields.error .field label{color:#9f3a38}.vi-ui.form .field.error .corner.label,.vi-ui.form .fields.error .field .corner.label{border-color:#9f3a38;color:#fff}.vi-ui.form .field.error input:not([type]),.vi-ui.form .field.error input[type=date],.vi-ui.form .field.error input[type=datetime-local],.vi-ui.form .field.error input[type=email],.vi-ui.form .field.error input[type=file],.vi-ui.form .field.error input[type=number],.vi-ui.form .field.error input[type=password],.vi-ui.form .field.error input[type=search],.vi-ui.form .field.error input[type=tel],.vi-ui.form .field.error input[type=text],.vi-ui.form .field.error input[type=time],.vi-ui.form .field.error input[type=url],.vi-ui.form .field.error select,.vi-ui.form .field.error textarea,.vi-ui.form .fields.error .field input:not([type]),.vi-ui.form .fields.error .field input[type=date],.vi-ui.form .fields.error .field input[type=datetime-local],.vi-ui.form .fields.error .field input[type=email],.vi-ui.form .fields.error .field input[type=file],.vi-ui.form .fields.error .field input[type=number],.vi-ui.form .fields.error .field input[type=password],.vi-ui.form .fields.error .field input[type=search],.vi-ui.form .fields.error .field input[type=tel],.vi-ui.form .fields.error .field input[type=text],.vi-ui.form .fields.error .field input[type=time],.vi-ui.form .fields.error .field input[type=url],.vi-ui.form .fields.error .field select,.vi-ui.form .fields.error .field textarea{background:#fff6f6;border-color:#e0b4b4;color:#9f3a38;border-radius:'';box-shadow:none}.vi-ui.form .field.error input:not([type]):focus,.vi-ui.form .field.error input[type=date]:focus,.vi-ui.form .field.error input[type=datetime-local]:focus,.vi-ui.form .field.error input[type=email]:focus,.vi-ui.form .field.error input[type=file]:focus,.vi-ui.form .field.error input[type=number]:focus,.vi-ui.form .field.error input[type=password]:focus,.vi-ui.form .field.error input[type=search]:focus,.vi-ui.form .field.error input[type=tel]:focus,.vi-ui.form .field.error input[type=text]:focus,.vi-ui.form .field.error input[type=time]:focus,.vi-ui.form .field.error input[type=url]:focus,.vi-ui.form .field.error select:focus,.vi-ui.form .field.error textarea:focus{background:#fff6f6;border-color:#e0b4b4;color:#9f3a38;-webkit-appearance:none;box-shadow:none}.vi-ui.form .field.error select{-webkit-appearance:menulist-button}.vi-ui.form .field.error .vi-ui.dropdown,.vi-ui.form .field.error .vi-ui.dropdown .item,.vi-ui.form .field.error .vi-ui.dropdown .text,.vi-ui.form .fields.error .field .vi-ui.dropdown,.vi-ui.form .fields.error .field .vi-ui.dropdown .item{background:#fff6f6;color:#9f3a38}.vi-ui.form .field.error .vi-ui.dropdown,.vi-ui.form .field.error .vi-ui.dropdown:hover,.vi-ui.form .fields.error .field .vi-ui.dropdown,.vi-ui.form .fields.error .field .vi-ui.dropdown:hover{border-color:#e0b4b4!important}.vi-ui.form .field.error .vi-ui.dropdown:hover .menu,.vi-ui.form .fields.error .field .vi-ui.dropdown:hover .menu{border-color:#e0b4b4}.vi-ui.form .field.error .vi-ui.multiple.selection.dropdown>.label,.vi-ui.form .fields.error .field .vi-ui.multiple.selection.dropdown>.label{background-color:#eacbcb;color:#9f3a38}.vi-ui.form .field.error .vi-ui.dropdown .menu .item:hover,.vi-ui.form .field.error .vi-ui.dropdown .menu .selected.item,.vi-ui.form .fields.error .field .vi-ui.dropdown .menu .item:hover,.vi-ui.form .fields.error .field .vi-ui.dropdown .menu .selected.item{background-color:#fbe7e7}.vi-ui.form .field.error .vi-ui.dropdown .menu .active.item,.vi-ui.form .fields.error .field .vi-ui.dropdown .menu .active.item{background-color:#fdcfcf!important}.vi-ui.form .field.error .checkbox:not(.toggle):not(.slider) .box,.vi-ui.form .field.error .checkbox:not(.toggle):not(.slider) label,.vi-ui.form .fields.error .field .checkbox:not(.toggle):not(.slider) .box,.vi-ui.form .fields.error .field .checkbox:not(.toggle):not(.slider) label{color:#9f3a38}.vi-ui.form .field.error .checkbox:not(.toggle):not(.slider) .box:before,.vi-ui.form .field.error .checkbox:not(.toggle):not(.slider) label:before,.vi-ui.form .fields.error .field .checkbox:not(.toggle):not(.slider) .box:before,.vi-ui.form .fields.error .field .checkbox:not(.toggle):not(.slider) label:before{background:#fff6f6;border-color:#e0b4b4}.vi-ui.form .disabled.field,.vi-ui.form .disabled.fields .field,.vi-ui.form .field :disabled{pointer-events:none;opacity:.45}.vi-ui.form .field.disabled>label,.vi-ui.form .fields.disabled>label{opacity:.45}.vi-ui.form .field.disabled :disabled{opacity:1}.vi-ui.loading.form{position:relative;cursor:default;pointer-events:none}.vi-ui.loading.form:before{position:absolute;content:'';top:0;left:0;background:rgba(255,255,255,.8);width:100%;height:100%;z-index:100}.vi-ui.loading.form:after{position:absolute;content:'';top:50%;left:50%;margin:-1.5em 0 0 -1.5em;width:3em;height:3em;-webkit-animation:form-spin .6s linear;animation:form-spin .6s linear;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;border-radius:500rem;border-color:#767676 rgba(0,0,0,.1) rgba(0,0,0,.1);border-style:solid;border-width:.2em;box-shadow:0 0 0 1px transparent;visibility:visible;z-index:101}.vi-ui.form .required.field>.checkbox:after,.vi-ui.form .required.field>label:after,.vi-ui.form .required.fields.grouped>label:after,.vi-ui.form .required.fields:not(.grouped)>.field>.checkbox:after,.vi-ui.form .required.fields:not(.grouped)>.field>label:after{margin:-.2em 0 0 .2em;content:'*';color:#db2828}.vi-ui.form .required.field>label:after,.vi-ui.form .required.fields.grouped>label:after,.vi-ui.form .required.fields:not(.grouped)>.field>label:after{display:inline-block;vertical-align:top}.vi-ui.form .required.field>.checkbox:after,.vi-ui.form .required.fields:not(.grouped)>.field>.checkbox:after{position:absolute;top:0;left:100%}.vi-ui.form .inverted.segment .vi-ui.checkbox .box,.vi-ui.form .inverted.segment .vi-ui.checkbox label,.vi-ui.form .inverted.segment label,.vi-ui.inverted.form .inline.field>label,.vi-ui.inverted.form .inline.field>p,.vi-ui.inverted.form .inline.fields .field>label,.vi-ui.inverted.form .inline.fields .field>p,.vi-ui.inverted.form .inline.fields>label,.vi-ui.inverted.form .vi-ui.checkbox .box,.vi-ui.inverted.form .vi-ui.checkbox label,.vi-ui.inverted.form label{color:rgba(255,255,255,.9)}.vi-ui.inverted.form input:not([type]),.vi-ui.inverted.form input[type=date],.vi-ui.inverted.form input[type=datetime-local],.vi-ui.inverted.form input[type=email],.vi-ui.inverted.form input[type=file],.vi-ui.inverted.form input[type=number],.vi-ui.inverted.form input[type=password],.vi-ui.inverted.form input[type=search],.vi-ui.inverted.form input[type=tel],.vi-ui.inverted.form input[type=text],.vi-ui.inverted.form input[type=time],.vi-ui.inverted.form input[type=url]{background:#fff;border-color:rgba(255,255,255,.1);color:rgba(0,0,0,.87);box-shadow:none}.vi-ui.form .grouped.fields{display:block;margin:0 0 1em}.vi-ui.form .grouped.fields:last-child{margin-bottom:0}.vi-ui.form .grouped.fields>label{margin:0 0 .28571429rem;color:rgba(0,0,0,.87);font-size:.92857143em;font-weight:700;text-transform:none}.vi-ui.form .grouped.fields .field,.vi-ui.form .grouped.inline.fields .field{display:block;margin:.5em 0;padding:0}.vi-ui.form .fields{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-direction:row;flex-direction:row;margin:0 -.5em 1em}.vi-ui.form .fields>.field{-webkit-box-flex:0;-ms-flex:0 1 auto;flex:0 1 auto;padding-left:.5em;padding-right:.5em}.vi-ui.form .fields>.field:first-child{border-left:none;box-shadow:none}.vi-ui.form .two.fields>.field,.vi-ui.form .two.fields>.fields{width:50%}.vi-ui.form .three.fields>.field,.vi-ui.form .three.fields>.fields{width:33.33333333%}.vi-ui.form .four.fields>.field,.vi-ui.form .four.fields>.fields{width:25%}.vi-ui.form .five.fields>.field,.vi-ui.form .five.fields>.fields{width:20%}.vi-ui.form .six.fields>.field,.vi-ui.form .six.fields>.fields{width:16.66666667%}.vi-ui.form .seven.fields>.field,.vi-ui.form .seven.fields>.fields{width:14.28571429%}.vi-ui.form .eight.fields>.field,.vi-ui.form .eight.fields>.fields{width:12.5%}.vi-ui.form .nine.fields>.field,.vi-ui.form .nine.fields>.fields{width:11.11111111%}.vi-ui.form .ten.fields>.field,.vi-ui.form .ten.fields>.fields{width:10%}@media only screen and (max-width:767px){.vi-ui.form .fields{-ms-flex-wrap:wrap;flex-wrap:wrap}.vi-ui.form:not(.unstackable) .eight.fields:not(.unstackable)>.field,.vi-ui.form:not(.unstackable) .eight.fields:not(.unstackable)>.fields,.vi-ui.form:not(.unstackable) .five.fields:not(.unstackable)>.field,.vi-ui.form:not(.unstackable) .five.fields:not(.unstackable)>.fields,.vi-ui.form:not(.unstackable) .four.fields:not(.unstackable)>.field,.vi-ui.form:not(.unstackable) .four.fields:not(.unstackable)>.fields,.vi-ui.form:not(.unstackable) .nine.fields:not(.unstackable)>.field,.vi-ui.form:not(.unstackable) .nine.fields:not(.unstackable)>.fields,.vi-ui.form:not(.unstackable) .seven.fields:not(.unstackable)>.field,.vi-ui.form:not(.unstackable) .seven.fields:not(.unstackable)>.fields,.vi-ui.form:not(.unstackable) .six.fields:not(.unstackable)>.field,.vi-ui.form:not(.unstackable) .six.fields:not(.unstackable)>.fields,.vi-ui.form:not(.unstackable) .ten.fields:not(.unstackable)>.field,.vi-ui.form:not(.unstackable) .ten.fields:not(.unstackable)>.fields,.vi-ui.form:not(.unstackable) .three.fields:not(.unstackable)>.field,.vi-ui.form:not(.unstackable) .three.fields:not(.unstackable)>.fields,.vi-ui.form:not(.unstackable) .two.fields:not(.unstackable)>.field,.vi-ui.form:not(.unstackable) .two.fields:not(.unstackable)>.fields,.vi-ui.form:not(.unstackable) [class*="equal width"].fields:not(.unstackable)>.field,.vi-ui[class*="equal width"].form:not(.unstackable) .fields>.field{width:100%!important;margin:0 0 1em}}.vi-ui.form .fields .wide.field{width:6.25%;padding-left:.5em;padding-right:.5em}.vi-ui.form .one.wide.field{width:6.25%!important}.vi-ui.form .two.wide.field{width:12.5%!important}.vi-ui.form .three.wide.field{width:18.75%!important}.vi-ui.form .four.wide.field{width:25%!important}.vi-ui.form .five.wide.field{width:31.25%!important}.vi-ui.form .six.wide.field{width:37.5%!important}.vi-ui.form .seven.wide.field{width:43.75%!important}.vi-ui.form .eight.wide.field{width:50%!important}.vi-ui.form .nine.wide.field{width:56.25%!important}.vi-ui.form .ten.wide.field{width:62.5%!important}.vi-ui.form .eleven.wide.field{width:68.75%!important}.vi-ui.form .twelve.wide.field{width:75%!important}.vi-ui.form .thirteen.wide.field{width:81.25%!important}.vi-ui.form .fourteen.wide.field{width:87.5%!important}.vi-ui.form .fifteen.wide.field{width:93.75%!important}.vi-ui.form .sixteen.wide.field{width:100%!important}@media only screen and (max-width:767px){.vi-ui.form:not(.unstackable) .fields:not(.unstackable)>.eight.wide.field,.vi-ui.form:not(.unstackable) .fields:not(.unstackable)>.eleven.wide.field,.vi-ui.form:not(.unstackable) .fields:not(.unstackable)>.fifteen.wide.field,.vi-ui.form:not(.unstackable) .fields:not(.unstackable)>.five.wide.field,.vi-ui.form:not(.unstackable) .fields:not(.unstackable)>.four.wide.field,.vi-ui.form:not(.unstackable) .fields:not(.unstackable)>.fourteen.wide.field,.vi-ui.form:not(.unstackable) .fields:not(.unstackable)>.nine.wide.field,.vi-ui.form:not(.unstackable) .fields:not(.unstackable)>.seven.wide.field,.vi-ui.form:not(.unstackable) .fields:not(.unstackable)>.six.wide.field,.vi-ui.form:not(.unstackable) .fields:not(.unstackable)>.sixteen.wide.field,.vi-ui.form:not(.unstackable) .fields:not(.unstackable)>.ten.wide.field,.vi-ui.form:not(.unstackable) .fields:not(.unstackable)>.thirteen.wide.field,.vi-ui.form:not(.unstackable) .fields:not(.unstackable)>.three.wide.field,.vi-ui.form:not(.unstackable) .fields:not(.unstackable)>.twelve.wide.field,.vi-ui.form:not(.unstackable) .fields:not(.unstackable)>.two.wide.field,.vi-ui.form:not(.unstackable) .five.fields:not(.unstackable)>.field,.vi-ui.form:not(.unstackable) .five.fields:not(.unstackable)>.fields,.vi-ui.form:not(.unstackable) .four.fields:not(.unstackable)>.field,.vi-ui.form:not(.unstackable) .four.fields:not(.unstackable)>.fields,.vi-ui.form:not(.unstackable) .three.fields:not(.unstackable)>.field,.vi-ui.form:not(.unstackable) .three.fields:not(.unstackable)>.fields,.vi-ui.form:not(.unstackable) .two.fields:not(.unstackable)>.field,.vi-ui.form:not(.unstackable) .two.fields:not(.unstackable)>.fields{width:100%!important}.vi-ui.form .fields{margin-bottom:0}}.vi-ui.form [class*="equal width"].fields>.field,.vi-ui[class*="equal width"].form .fields>.field{width:100%;-webkit-box-flex:1;-ms-flex:1 1 auto;flex:1 1 auto}.vi-ui.form .inline.fields{margin:0 0 1em;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.vi-ui.form .inline.fields .field{margin:0;padding:0 1em 0 0}.vi-ui.form .inline.field>label,.vi-ui.form .inline.field>p,.vi-ui.form .inline.fields .field>label,.vi-ui.form .inline.fields .field>p{display:inline-block;width:auto;margin-top:0;margin-bottom:0;vertical-align:baseline;font-size:.92857143em;font-weight:700;color:rgba(0,0,0,.87);text-transform:none}.vi-ui.form .inline.fields>label{display:inline-block;width:auto;vertical-align:baseline;font-size:.92857143em;font-weight:700;color:rgba(0,0,0,.87);text-transform:none;margin:.035714em 1em 0 0}.vi-ui.form .inline.field>input,.vi-ui.form .inline.field>select,.vi-ui.form .inline.fields .field>input,.vi-ui.form .inline.fields .field>select{display:inline-block;width:auto;margin-top:0;margin-bottom:0;vertical-align:middle;font-size:1em}.vi-ui.form .inline.field>:first-child,.vi-ui.form .inline.fields .field>:first-child{margin:0 .85714286em 0 0}.vi-ui.form .inline.field>:only-child,.vi-ui.form .inline.fields .field>:only-child{margin:0}.vi-ui.form .inline.fields .wide.field{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.vi-ui.form .inline.fields .wide.field>input,.vi-ui.form .inline.fields .wide.field>select{width:100%}.vi-ui.mini.form{font-size:.78571429rem}.vi-ui.tiny.form{font-size:.85714286rem}.vi-ui.small.form{font-size:.92857143rem}.vi-ui.large.form{font-size:1.14285714rem}.vi-ui.big.form{font-size:1.28571429rem}.vi-ui.huge.form{font-size:1.42857143rem}.vi-ui.massive.form{font-size:1.71428571rem}