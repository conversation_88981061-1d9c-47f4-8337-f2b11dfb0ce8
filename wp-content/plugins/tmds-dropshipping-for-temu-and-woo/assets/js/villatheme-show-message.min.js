jQuery(document).ready((function(){"use strict";jQuery(document.body).on("villatheme_show_messages",(function(e,t){if(!t||!t.length)return!1;let s=setInterval((function(e){let t=e[0]||{};return t.title||t.message?(t.title?jQuery(document.body).trigger("villatheme_show_message",[t.title,t.status,t.message,t.is_central_title||!1,t.time||4500]):jQuery(document.body).trigger("villatheme_show_message",[t.message,t.status,"",t.is_central_title||!1,t.time||4500]),e.shift(),e.length?void 0:(clearInterval(s),!1)):(clearInterval(s),!1)}),500,t)})),jQuery(document.body).on("villatheme_show_message",(function(e,t,s="",a="",i=!1,o=0){jQuery(".villatheme-show-message-container").length||jQuery("body").append('<div class="villatheme-show-message-container"></div>');let l,n=jQuery(".villatheme-show-message-container"),m="villatheme-show-message-title";a&&(m+=" villatheme-show-message-title1",a='<div class="villatheme-show-message-content">'+a+"</div>"),i&&(m+=" villatheme-show-message-title-central"),l='<div class="villatheme-show-message-item villatheme-show-message-new-added-item">',l+='<div class="'+m+'">'+t+"</div>",l+='<span class="villatheme-show-message-item-close dashicons dashicons-no-alt"></span>'+a,l+="</div></div>",l=jQuery(l),n.prepend(l),"string"==typeof s?l.addClass("villatheme-show-message-message-"+s):jQuery.each(s,(function(e,t){l.addClass("villatheme-show-message-message-"+t)}));let r=jQuery(document.body).triggerHandler("villatheme_show_message_timeout",[l,o]);r.length>0&&(l.on("mouseenter",(function(){for(let e in r)clearTimeout(r[e])})),l.on("mouseleave",(function(){for(let e in r)clearTimeout(r[e]);r=jQuery(document.body).triggerHandler("villatheme_show_message_timeout",[l,o])}))),l.on("click",(function(e){e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation()})),l.on("click",".villatheme-show-message-item-close",(function(e){if(e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),r.length>0)for(let e in r)clearTimeout(r[e]);l.addClass("villatheme-show-message-new-added-item"),setTimeout((function(){l.remove()}),300)}))})),jQuery(document.body).on("villatheme_show_message_timeout",(function(e,t,s){let a=[];return setTimeout((function(){t.removeClass("villatheme-show-message-new-added-item"),s>0&&(a.push(setTimeout((function(){t.addClass("villatheme-show-message-new-added-item")}),s)),a.push(setTimeout((function(){t.remove()}),s+300)))}),10),a}))}));