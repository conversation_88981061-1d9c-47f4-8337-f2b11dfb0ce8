function tmds_ajax(e){"use strict";window.fetch(e.url,{method:e.type,headers:{"Content-Type":"application/x-www-form-urlencoded; charset=UTF-8"},body:e.data}).then((e=>e.ok?e.json():e.text().then((t=>{throw{statusText:e.statusText,responseText:t}})))).then(e.success).catch((t=>e.error&&e.error(t))).finally((()=>e.complete&&e.complete()))}jQuery(document).ready((function(e){"use strict";if("undefined"==typeof tmds_params)return;if(tmds_params.go_to_tmds_bt&&e(".woocommerce-BlankState-buttons").length)return void e(".woocommerce-BlankState-buttons").append(tmds_params.go_to_tmds_bt);if(!tmds_params.nonce)return;let t=tmds_params.nonce,s=tmds_params.ajax_url;e(".vi-ui.tabular.menu").length&&e(".vi-ui.tabular.menu .item").tab({history:!0,historyType:"hash"}),e(".vi-ui.accordion").length&&e(".vi-ui.accordion:not(.tmds-accordion-init)").addClass("tmds-accordion-init").accordion("refresh"),e(".vi-ui.checkbox:not(.tmds-checkbox-init)").addClass("tmds-checkbox-init").off().checkbox(),e(".vi-ui.dropdown:not(.tmds-dropdown-init)").addClass("tmds-dropdown-init").off().dropdown(),e(document).on("click",".tmds-save-settings",(function(t){let s=e("#tmds-import_currency_rate");if(s.length&&!s.val())return e(document.body).trigger("villatheme_show_message",["Please enter Import products currency exchange rate",["error"],"",!1,4500]),s.trigger("focus"),t.preventDefault(),t.stopPropagation(),!1;e(this).addClass("loading")})),e(document).on("change","input[type=checkbox]",(function(){let t=e(this).prop("checked")?1:0,s=e(this).parent().find("input[type=hidden]");s.val(t),e(this).prop("checked")?e(".tmds-"+s.attr("name")+"-disable-class").addClass("tmds-hidden"):e(".tmds-"+s.attr("name")+"-disable-class").removeClass("tmds-hidden")})),e(document).on("change","select#tmds-import_currency_rate",(function(){let t=e(this).closest("tr").find(".tmds-price-exchange-rate");t.attr("name",t.data("name").replace("{currency_code}",e(this).val()))})),e(document).on("change",'select[name="plus_value_type[]"], select[name="price_default[plus_value_type]"]',(function(){let t=e(this).closest("tr");switch(e(this).val()){case"fixed":t.find(".tmds-value-label-left").html("+"),t.find(".tmds-value-label-right").html(t.closest(".price-rule").data("currency_symbol"));break;case"percent":t.find(".tmds-value-label-left").html("+"),t.find(".tmds-value-label-right").html("%");break;case"multiply":t.find(".tmds-value-label-left").html("x"),t.find(".tmds-value-label-right").html("");break;default:t.find(".tmds-value-label-left").html("="),t.find(".tmds-value-label-right").html(t.closest(".price-rule").data("currency_symbol"))}})),e(document).on("click",".tmds-price-rule-add",(function(){let t=e(this).closest(".accordion"),s=t.find(".tmds-price-rule-row").last().clone();s.find(".tmds-input-reset").val(""),s.find(".tmds-price-to").val(""),s.find(".vi-ui.dropdown").off().dropdown(),t.find(".tmds-price-rule-container").append(s)})),e(document).on("click",".tmds-price-rule-remove",(function(){let t=e(this).closest(".accordion").find(".tmds-price-rule-row"),s=e(this).closest(".tmds-price-rule-row");t.length>1&&confirm("Do you want to remove this row?")&&(s.fadeOut(300),setTimeout((()=>s.remove()),300))})),e(".tmds-search-select2:not(.tmds-search-select2-init)").each((function(){let s=e(this),n="",a="";switch(s.data("type_select2")){case"category":n="Please enter category name to search",a="tmds_search_cate";break;case"tag":n="Please enter tag to search",a="tmds_search_tag";break;case"product":n="Please enter product title to search",a="tmds_search_product"}s.addClass("tmds-search-select2-init").select2(function(e,s,n,a){let o={closeOnSelect:n,placeholder:e,cache:!0};s&&(o.minimumInputLength=a,o.escapeMarkup=function(e){return e},o.ajax={url:`admin-ajax.php?action=${s}&tmds_nonce=${t}`,dataType:"json",type:"GET",quietMillis:50,delay:250,data:function(e){return{keyword:e.term}},processResults:function(e){return{results:e}},cache:!0});return o}(n,a,!1,2))})),e(document).on("change",".tmds-select-plugin",(function(){let t=e(".tmds-select-plugin:checked").length;e(".tmds-finish span").text(t>0?"Install & Return to Dashboard":"Return to Dashboard"),t||e(".tmds-toggle-select-plugin").prop("checked",!1)})),e(document).on("change",".tmds-toggle-select-plugin",(function(){let t=e(this).prop("checked");e(".tmds-select-plugin").prop("checked",t),e(".tmds-finish span").text(t?"Install & Return to Dashboard":"Return to Dashboard")})),e(document).on("click",".tmds-finish",(function(){let n=e(this),a=e(".tmds-select-plugin:checked").map(((t,s)=>e(s).data("plugin_slug"))).toArray();if(a.length){n.addClass("loading");let o=!1;tmds_ajax({url:s,type:"POST",data:e.param({action:"tmds_setup_install_plugins",tmds_nonce:t,install_plugins:a}),success:function(t){"success"===t.status&&t?.installed_plugins&&(o=t.installed_plugins),t?.message&&e.each(t?.message,(function(e,s){jQuery(document.body).trigger("villatheme_show_message",[s,[t.status],"",!1,4500])}))},error:function(e){console.log(e),o=!1,jQuery(document.body).trigger("villatheme_show_message",[e.statusText,["error"],"-1"===e.responseText?"":e.responseText,!1,4500])},complete:function(){if(!o)return n.removeClass("loading"),!1;tmds_ajax({url:s,type:"POST",data:e.param({action:"tmds_setup_activate_plugins",tmds_nonce:t,install_plugins:o}),error:function(e){console.log(e),n.removeClass("loading"),jQuery(document.body).trigger("villatheme_show_message",[e.statusText,["error"],"-1"===e.responseText?"":e.responseText,!1,4500])},success:function(e){!e.success&&e.data&&jQuery(document.body).trigger("villatheme_show_message",[e.data,["error"],"",!1,4500])},complete:function(e){n.removeClass("loading"),window.location.href=tmds_params.settings_page_url}})}})}else window.location.href=tmds_params.settings_page_url;return!1}))}));