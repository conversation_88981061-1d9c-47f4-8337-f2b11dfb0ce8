jQuery(document).ready(function ($) {
    'use strict';
    if (typeof tmds_params === "undefined") {
        return;
    }
    if (tmds_params.go_to_tmds_bt && $('.woocommerce-BlankState-buttons').length) {
        $('.woocommerce-BlankState-buttons').append(tmds_params.go_to_tmds_bt)
        return;
    }
    if (!tmds_params.nonce) {
        return;
    }
    let ajax_nonce = tmds_params.nonce, ajax_url = tmds_params.ajax_url;
    if ($('.vi-ui.tabular.menu').length) {
        $('.vi-ui.tabular.menu .item').tab({history: true, historyType: 'hash'});
    }
    if ($('.vi-ui.accordion').length) {
        $('.vi-ui.accordion:not(.tmds-accordion-init)').addClass('tmds-accordion-init').accordion('refresh');
        // $('.vi-ui.accordion:not(.tmds-accordion-init)').addClass('tmds-accordion-init').vi_accordion('refresh');
    }
    $('.vi-ui.checkbox:not(.tmds-checkbox-init)').addClass('tmds-checkbox-init').off().checkbox();
    $('.vi-ui.dropdown:not(.tmds-dropdown-init)').addClass('tmds-dropdown-init').off().dropdown();

    $(document).on('click', '.tmds-save-settings', function (e) {
        let rateInput = $('#tmds-import_currency_rate');
        if (rateInput.length && !rateInput.val()) {
            $(document.body).trigger('villatheme_show_message', ['Please enter Import products currency exchange rate', ['error'], '', false, 4500]);
            rateInput.trigger('focus');
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
        $(this).addClass('loading');
    });
    //
    $(document).on('change', 'input[type=checkbox]', function () {
        let val = $(this).prop('checked') ? 1: 0,
            $input = $(this).parent().find('input[type=hidden]');
        $input.val(val);
        if ($(this).prop('checked')){
            $('.tmds-'+$input.attr('name')+'-disable-class').addClass('tmds-hidden');
        }else {
            $('.tmds-'+$input.attr('name')+'-disable-class').removeClass('tmds-hidden');
        }
    });
    //price rule
    $(document).on('change', 'select#tmds-import_currency_rate', function () {
        let import_currency_rate_input = $(this).closest('tr').find('.tmds-price-exchange-rate');
        import_currency_rate_input.attr('name', import_currency_rate_input.data('name').replace('{currency_code}', $(this).val()));
    });
    $(document).on('change', 'select[name="plus_value_type[]"], select[name="price_default[plus_value_type]"]', function () {
        let $current = $(this).closest('tr');
        switch ($(this).val()) {
            case 'fixed':
                $current.find('.tmds-value-label-left').html('+');
                $current.find('.tmds-value-label-right').html($current.closest('.price-rule').data('currency_symbol'));
                break;
            case 'percent':
                $current.find('.tmds-value-label-left').html('+');
                $current.find('.tmds-value-label-right').html('%');
                break;
            case 'multiply':
                $current.find('.tmds-value-label-left').html('x');
                $current.find('.tmds-value-label-right').html('');
                break;
            default:
                $current.find('.tmds-value-label-left').html('=');
                $current.find('.tmds-value-label-right').html($current.closest('.price-rule').data('currency_symbol'));
        }
    });
    $(document).on('click', '.tmds-price-rule-add', function () {
        let wrap = $(this).closest('.accordion');
        let $rows = wrap.find('.tmds-price-rule-row'),
            $lastRow = $rows.last(),
            $newRow = $lastRow.clone();
        $newRow.find('.tmds-input-reset').val('');
        $newRow.find('.tmds-price-to').val('');
        $newRow.find('.vi-ui.dropdown').off().dropdown();
        wrap.find('.tmds-price-rule-container').append($newRow);
    });
    $(document).on('click', '.tmds-price-rule-remove', function () {
        let wrap = $(this).closest('.accordion');
        let $rows = wrap.find('.tmds-price-rule-row'),
            $row = $(this).closest('.tmds-price-rule-row');
        if ($rows.length > 1) {
            if (confirm('Do you want to remove this row?')) {
                $row.fadeOut(300);
                setTimeout(() => $row.remove(), 300);
            }
        }
    });
    //price rule

    //search select2
    $('.tmds-search-select2:not(.tmds-search-select2-init)').each(function () {
        let select = $(this);
        let close_on_select = false, min_input = 2, placeholder = '', action = '', type_select2 = select.data('type_select2');
        switch (type_select2) {
            case 'category':
                placeholder = 'Please enter category name to search';
                action = 'tmds_search_cate';
                break;
            case 'tag':
                placeholder = 'Please enter tag to search';
                action = 'tmds_search_tag';
                break;
            case 'product':
                placeholder = 'Please enter product title to search';
                action = 'tmds_search_product';
                break;
        }
        select.addClass('tmds-search-select2-init').select2(select2_params(placeholder, action, close_on_select, min_input));
    });

    function select2_params(placeholder, action, close_on_select, min_input) {
        let result = {
            closeOnSelect: close_on_select,
            placeholder: placeholder,
            cache: true
        };
        if (action) {
            result['minimumInputLength'] = min_input;
            result['escapeMarkup'] = function (markup) {
                return markup;
            };
            result['ajax'] = {
                url: `admin-ajax.php?action=${action}&tmds_nonce=${ajax_nonce}`,
                dataType: 'json',
                type: "GET",
                quietMillis: 50,
                delay: 250,
                data: function (params) {
                    return {
                        keyword: params.term
                    };
                },
                processResults: function (data) {
                    return {
                        results: data
                    };
                },
                cache: true
            };
        }
        return result;
    }

    //search select2

    //recommend_plugins
    $(document).on('change', '.tmds-select-plugin', function () {
        let checkedCount = $('.tmds-select-plugin:checked').length;
        $('.tmds-finish span').text(checkedCount > 0 ? 'Install & Return to Dashboard' : 'Return to Dashboard');
        if (!checkedCount) {
            $('.tmds-toggle-select-plugin').prop('checked', false);
        }
    });
    $(document).on('change', '.tmds-toggle-select-plugin', function () {
        let checked = $(this).prop('checked');
        $('.tmds-select-plugin').prop('checked', checked);
        $('.tmds-finish span').text(checked ? 'Install & Return to Dashboard' : 'Return to Dashboard');
    });
    $(document).on('click', '.tmds-finish', function () {
        let $button = $(this);
        let install_plugins = $('.tmds-select-plugin:checked').map((i, el) => $(el).data('plugin_slug')).toArray();
        if (install_plugins.length) {
            $button.addClass('loading');
            let active_plugin = false;
            tmds_ajax({
                url: ajax_url,
                type: 'POST',
                data: $.param({
                    action: 'tmds_setup_install_plugins',
                    tmds_nonce: ajax_nonce,
                    install_plugins: install_plugins,
                }),
                success: function (response) {
                    if ( response.status === 'success' && response?.installed_plugins) {
                        active_plugin = response.installed_plugins;
                    }
                    if (response?.message) {
                        $.each(response?.message, function (k, v) {
                            jQuery(document.body).trigger('villatheme_show_message', [v, [response.status], '', false, 4500]);
                        });
                    }
                },
                error: function (err) {
                    active_plugin = false;
                    jQuery(document.body).trigger('villatheme_show_message', [err.statusText, ['error'], err.responseText === '-1' ? '' : err.responseText, false, 4500]);
                },
                complete: function () {
                    if (!active_plugin) {
                        $button.removeClass('loading');
                        return false;
                    }
                    tmds_ajax({
                        url: ajax_url,
                        type: 'POST',
                        data: $.param({
                            action: 'tmds_setup_activate_plugins',
                            tmds_nonce: ajax_nonce,
                            install_plugins: active_plugin,
                        }),
                        error: function (err) {
                            $button.removeClass('loading');
                            jQuery(document.body).trigger('villatheme_show_message', [err.statusText, ['error'], err.responseText === '-1' ? '' : err.responseText, false, 4500]);
                        },
                        success: function (response) {
                            if (!response.success && response.data) {
                                jQuery(document.body).trigger('villatheme_show_message', [response.data, ['error'], '', false, 4500]);
                            }
                        },
                        complete: function (response) {
                            $button.removeClass('loading');
                            window.location.href = tmds_params.settings_page_url;
                        }
                    });
                }
            });
        } else {
            window.location.href = tmds_params.settings_page_url;
        }
        return false;
    });
    //recommend_plugins
});

function tmds_ajax(options) {
    'use strict';
    window.fetch(options.url, {
        method: options.type,
        headers: {'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'},
        body: options.data
    }).then(response => {
        if (!response.ok) {
            return response.text().then(text => {
                throw {statusText: response.statusText, responseText: text}
            });
        }
        return response.json();
    }).then(options.success).catch(error => options.error && options.error(error)).finally(() => options.complete && options.complete());
}