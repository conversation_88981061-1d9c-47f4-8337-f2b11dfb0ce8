!function(E,O,w,k){"use strict";O=void 0!==O&&O.Math==Math?O:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),E.fn.tab=function(r){var l,d=E.isFunction(this)?E(O):E(this),u=d.selector||"",b=(new Date).getTime(),g=[],f=r,F="string"==typeof f,S=[].slice.call(arguments,1),j=!1;return d.each(function(){var h,o,p,m,v,y=E.isPlainObject(r)?E.extend(!0,{},E.fn.tab.settings,r):E.extend({},E.fn.tab.settings),T=y.className,L=y.metadata,t=y.selector,x=y.error,e="."+y.namespace,a="module-"+y.namespace,A=E(this),n={},P=!0,i=0,s=this,c=A.data(a),C={initialize:function(){C.debug("Initializing tab menu item",A),C.fix.callbacks(),<PERSON><PERSON>determineTabs(),<PERSON>.debug("Determining tabs",y.context,o),y.auto&&C.set.auto(),C.bind.events(),y.history&&!j&&(C.initializeHistory(),j=!0),C.instantiate()},instantiate:function(){C.verbose("Storing instance of module",C),c=C,A.data(a,C)},destroy:function(){C.debug("Destroying tabs",A),A.removeData(a).off(e)},bind:{events:function(){E.isWindow(s)||(C.debug("Attaching tab activation events to element",A),A.on("click"+e,C.event.click))}},determineTabs:function(){var e;"parent"===y.context?(0<A.closest(t.ui).length?(e=A.closest(t.ui),C.verbose("Using closest UI element as parent",e)):e=A,h=e.parent(),C.verbose("Determined parent element for creating context",h)):y.context?(h=E(y.context),C.verbose("Using selector for tab context",y.context,h)):h=E("body"),y.childrenOnly?(o=h.children(t.tabs),C.debug("Searching tab context children for tabs",h,o)):(o=h.find(t.tabs),C.debug("Searching tab context for tabs",h,o))},fix:{callbacks:function(){E.isPlainObject(r)&&(r.onTabLoad||r.onTabInit)&&(r.onTabLoad&&(r.onLoad=r.onTabLoad,delete r.onTabLoad,C.error(x.legacyLoad,r.onLoad)),r.onTabInit&&(r.onFirstLoad=r.onTabInit,delete r.onTabInit,C.error(x.legacyInit,r.onFirstLoad)),y=E.extend(!0,{},E.fn.tab.settings,r))}},initializeHistory:function(){if(C.debug("Initializing page state"),E.address===k)return C.error(x.state),!1;if("state"==y.historyType){if(C.debug("Using HTML5 to manage state"),!1===y.path)return C.error(x.path),!1;E.address.history(!0).state(y.path)}E.address.bind("change",C.event.history.change)},event:{click:function(e){var t=E(this).data(L.tab);t!==k?(y.history?(C.verbose("Updating page state",e),E.address.value(t)):(C.verbose("Changing tab",e),C.changeTab(t)),e.preventDefault()):C.debug("No tab specified")},history:{change:function(e){var t=e.pathNames.join("/")||C.get.initialPath(),a=y.templates.determineTitle(t)||!1;C.performance.display(),C.debug("History change event",t,e),v=e,t!==k&&C.changeTab(t),a&&E.address.title(a)}}},refresh:function(){p&&(C.debug("Refreshing tab",p),C.changeTab(p))},cache:{read:function(e){return e!==k&&n[e]},add:function(e,t){e=e||p,C.debug("Adding cached content for",e),n[e]=t},remove:function(e){e=e||p,C.debug("Removing cached content for",e),delete n[e]}},set:{auto:function(){var e="string"==typeof y.path?y.path.replace(/\/$/,"")+"/{$tab}":"/{$tab}";C.verbose("Setting up automatic tab retrieval from server",e),E.isPlainObject(y.apiSettings)?y.apiSettings.url=e:y.apiSettings={url:e}},loading:function(e){var t=C.get.tabElement(e);t.hasClass(T.loading)||(C.verbose("Setting loading state for",t),t.addClass(T.loading).siblings(o).removeClass(T.active+" "+T.loading),0<t.length&&y.onRequest.call(t[0],e))},state:function(e){E.address.value(e)}},changeTab:function(u){var b=O.history&&O.history.pushState&&y.ignoreFirstLoad&&P,g=y.auto||E.isPlainObject(y.apiSettings),f=g&&!b?C.utilities.pathToArray(u):C.get.defaultPathArray(u);u=C.utilities.arrayToPath(f),E.each(f,function(e,t){var a,n,i,o,r=f.slice(0,e+1),s=C.utilities.arrayToPath(r),c=C.is.tab(s),l=e+1==f.length,d=C.get.tabElement(s);if(C.verbose("Looking for tab",t),c){if(C.verbose("Tab was found",t),p=s,m=C.utilities.filterArray(f,r),l?o=!0:(n=f.slice(0,e+2),i=C.utilities.arrayToPath(n),(o=!C.is.tab(i))&&C.verbose("Tab parameters found",n)),o&&g)return b?(C.debug("Ignoring remote content on first tab load",s),P=!1,C.cache.add(u,d.html()),C.activate.all(s),y.onFirstLoad.call(d[0],s,m,v),y.onLoad.call(d[0],s,m,v)):(C.activate.navigation(s),C.fetch.content(s,u)),!1;C.debug("Opened local tab",s),C.activate.all(s),C.cache.read(s)||(C.cache.add(s,!0),C.debug("First time tab loaded calling tab init"),y.onFirstLoad.call(d[0],s,m,v)),y.onLoad.call(d[0],s,m,v)}else{if(-1!=u.search("/")||""===u)return C.error(x.missingTab,A,h,s),!1;if(s=(a=E("#"+u+', a[name="'+u+'"]')).closest("[data-tab]").data(L.tab),d=C.get.tabElement(s),a&&0<a.length&&s)return C.debug("Anchor link used, opening parent tab",d,a),d.hasClass(T.active)||setTimeout(function(){C.scrollTo(a)},0),C.activate.all(s),C.cache.read(s)||(C.cache.add(s,!0),C.debug("First time tab loaded calling tab init"),y.onFirstLoad.call(d[0],s,m,v)),y.onLoad.call(d[0],s,m,v),!1}})},scrollTo:function(e){var t=!!(e&&0<e.length)&&e.offset().top;!1!==t&&(C.debug("Forcing scroll to an in-page link in a hidden tab",t,e),E(w).scrollTop(t))},update:{content:function(e,t,a){var n=C.get.tabElement(e),i=n[0];a=a!==k?a:y.evaluateScripts,"string"==typeof y.cacheType&&"dom"==y.cacheType.toLowerCase()&&"string"!=typeof t?n.empty().append(E(t).clone(!0)):a?(C.debug("Updating HTML and evaluating inline scripts",e,t),n.html(t)):(C.debug("Updating HTML",e,t),i.innerHTML=t)}},fetch:{content:function(t,a){var e,n,i=C.get.tabElement(t),o={dataType:"html",encodeParameters:!1,on:"now",cache:y.alwaysRefresh,headers:{"X-Remote":!0},onSuccess:function(e){"response"==y.cacheType&&C.cache.add(a,e),C.update.content(t,e),t==p?(C.debug("Content loaded",t),C.activate.tab(t)):C.debug("Content loaded in background",t),y.onFirstLoad.call(i[0],t,m,v),y.onLoad.call(i[0],t,m,v),y.loadOnce?C.cache.add(a,!0):"string"==typeof y.cacheType&&"dom"==y.cacheType.toLowerCase()&&0<i.children().length?setTimeout(function(){var e=(e=i.children().clone(!0)).not("script");C.cache.add(a,e)},0):C.cache.add(a,i.html())},urlData:{tab:a}},r=i.api("get request")||!1,s=r&&"pending"===r.state();a=a||t,n=C.cache.read(a),y.cache&&n?(C.activate.tab(t),C.debug("Adding cached content",a),y.loadOnce||("once"==y.evaluateScripts?C.update.content(t,n,!1):C.update.content(t,n)),y.onLoad.call(i[0],t,m,v)):s?(C.set.loading(t),C.debug("Content is already loading",a)):E.api!==k?(e=E.extend(!0,{},y.apiSettings,o),C.debug("Retrieving remote content",a,e),C.set.loading(t),i.api(e)):C.error(x.api)}},activate:{all:function(e){C.activate.tab(e),C.activate.navigation(e)},tab:function(e){var t=C.get.tabElement(e),a="siblings"==y.deactivate?t.siblings(o):o.not(t),n=t.hasClass(T.active);C.verbose("Showing tab content for",t),n||(t.addClass(T.active),a.removeClass(T.active+" "+T.loading),0<t.length&&y.onVisible.call(t[0],e))},navigation:function(e){var t=C.get.navElement(e),a="siblings"==y.deactivate?t.siblings(d):d.not(t),n=t.hasClass(T.active);C.verbose("Activating tab navigation for",t,e),n||(t.addClass(T.active),a.removeClass(T.active+" "+T.loading))}},deactivate:{all:function(){C.deactivate.navigation(),C.deactivate.tabs()},navigation:function(){d.removeClass(T.active)},tabs:function(){o.removeClass(T.active+" "+T.loading)}},is:{tab:function(e){return e!==k&&0<C.get.tabElement(e).length}},get:{initialPath:function(){return d.eq(0).data(L.tab)||o.eq(0).data(L.tab)},path:function(){return E.address.value()},defaultPathArray:function(e){return C.utilities.pathToArray(C.get.defaultPath(e))},defaultPath:function(e){var t=d.filter("[data-"+L.tab+'^="'+e+'/"]').eq(0).data(L.tab)||!1;if(t){if(C.debug("Found default tab",t),i<y.maxDepth)return i++,C.get.defaultPath(t);C.error(x.recursion)}else C.debug("No default tabs found for",e,o);return i=0,e},navElement:function(e){return e=e||p,d.filter("[data-"+L.tab+'="'+e+'"]')},tabElement:function(e){var t,a,n,i;return e=e||p,n=C.utilities.pathToArray(e),i=C.utilities.last(n),t=o.filter("[data-"+L.tab+'="'+e+'"]'),a=o.filter("[data-"+L.tab+'="'+i+'"]'),0<t.length?t:a},tab:function(){return p}},utilities:{filterArray:function(e,t){return E.grep(e,function(e){return-1==E.inArray(e,t)})},last:function(e){return!!E.isArray(e)&&e[e.length-1]},pathToArray:function(e){return e===k&&(e=p),"string"==typeof e?e.split("/"):[e]},arrayToPath:function(e){return!!E.isArray(e)&&e.join("/")}},setting:function(e,t){if(C.debug("Changing setting",e,t),E.isPlainObject(e))E.extend(!0,y,e);else{if(t===k)return y[e];E.isPlainObject(y[e])?E.extend(!0,y[e],t):y[e]=t}},internal:function(e,t){if(E.isPlainObject(e))E.extend(!0,C,e);else{if(t===k)return C[e];C[e]=t}},debug:function(){!y.silent&&y.debug&&(y.performance?C.performance.log(arguments):(C.debug=Function.prototype.bind.call(console.info,console,y.name+":"),C.debug.apply(console,arguments)))},verbose:function(){!y.silent&&y.verbose&&y.debug&&(y.performance?C.performance.log(arguments):(C.verbose=Function.prototype.bind.call(console.info,console,y.name+":"),C.verbose.apply(console,arguments)))},error:function(){y.silent||(C.error=Function.prototype.bind.call(console.error,console,y.name+":"),C.error.apply(console,arguments))},performance:{log:function(e){var t,a;y.performance&&(a=(t=(new Date).getTime())-(b||t),b=t,g.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:s,"Execution Time":a})),clearTimeout(C.performance.timer),C.performance.timer=setTimeout(C.performance.display,500)},display:function(){var e=y.name+":",a=0;b=!1,clearTimeout(C.performance.timer),E.each(g,function(e,t){a+=t["Execution Time"]}),e+=" "+a+"ms",u&&(e+=" '"+u+"'"),(console.group!==k||console.table!==k)&&0<g.length&&(console.groupCollapsed(e),console.table?console.table(g):E.each(g,function(e,t){console.log(t.Name+": "+t["Execution Time"]+"ms")}),console.groupEnd()),g=[]}},invoke:function(n,e,t){var i,o,a,r=c;return e=e||S,t=s||t,"string"==typeof n&&r!==k&&(n=n.split(/[\. ]/),i=n.length-1,E.each(n,function(e,t){var a=e!=i?t+n[e+1].charAt(0).toUpperCase()+n[e+1].slice(1):n;if(E.isPlainObject(r[a])&&e!=i)r=r[a];else{if(r[a]!==k)return o=r[a],!1;if(!E.isPlainObject(r[t])||e==i)return r[t]!==k?o=r[t]:C.error(x.method,n),!1;r=r[t]}})),E.isFunction(o)?a=o.apply(t,e):o!==k&&(a=o),E.isArray(l)?l.push(a):l!==k?l=[l,a]:a!==k&&(l=a),o}};F?(c===k&&C.initialize(),C.invoke(f)):(c!==k&&c.invoke("destroy"),C.initialize())}),l!==k?l:this},E.tab=function(){E(O).tab.apply(this,arguments)},E.fn.tab.settings={name:"Tab",namespace:"tab",silent:!1,debug:!1,verbose:!1,performance:!0,auto:!1,history:!1,historyType:"hash",path:!1,context:!1,childrenOnly:!1,maxDepth:25,deactivate:"siblings",alwaysRefresh:!1,cache:!0,loadOnce:!1,cacheType:"response",ignoreFirstLoad:!1,apiSettings:!1,evaluateScripts:"once",onFirstLoad:function(e,t,a){},onLoad:function(e,t,a){},onVisible:function(e,t,a){},onRequest:function(e,t,a){},templates:{determineTitle:function(e){}},error:{api:"You attempted to load content without API module",method:"The method you called is not defined",missingTab:"Activated tab cannot be found. Tabs are case-sensitive.",noContent:"The tab you specified is missing a content url.",path:"History enabled, but no path was specified",recursion:"Max recursive depth reached",legacyInit:"onTabInit has been renamed to onFirstLoad in 2.0, please adjust your code.",legacyLoad:"onTabLoad has been renamed to onLoad in 2.0. Please adjust your code",state:"History requires Asual's Address library <https://github.com/asual/jquery-address>"},metadata:{tab:"tab",loaded:"loaded",promise:"promise"},className:{loading:"loading",active:"active"},selector:{tabs:".vi-ui.tab",ui:".vi-ui"}}}(jQuery,window,document);