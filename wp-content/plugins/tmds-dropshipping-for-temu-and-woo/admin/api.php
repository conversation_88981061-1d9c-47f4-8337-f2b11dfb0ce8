<?php

defined( 'ABSPATH' ) || exit;

class TMDS_Admin_Api {
	protected $namespace ;
	protected static $settings;
	public function __construct() {
		self::$settings = TMDS_DATA::get_instance();
		$this->namespace = self::$settings::$prefix;
		add_action( 'rest_api_init', array( $this, 'register_api' ) );
		add_filter( 'woocommerce_rest_is_request_to_rest_api', [ $this, 'rest_is_request_to_rest_api' ] );
	}
	public function rest_is_request_to_rest_api( $is_request_to_rest_api ) {
		if ( empty( $_SERVER['REQUEST_URI'] ) ) {
			return false;
		}
		$rest_prefix = trailingslashit( rest_get_url_prefix() );
		$request_uri = esc_url_raw( wp_unslash( $_SERVER['REQUEST_URI'] ) );
		if ( false !== strpos( $request_uri, $rest_prefix . $this->namespace.'/' ) ) {
			$is_request_to_rest_api = true;
		}

		return $is_request_to_rest_api;
	}
	public function register_api() {
		register_rest_route( $this->namespace, '/check_plugin_active', [
			'methods'             => WP_REST_Server::CREATABLE,
			'callback'            => [ $this, 'check_plugin_active' ],
			'permission_callback' => array( $this, 'plugin_permissions_check' ),
		] );
		/*Auth method*/
		register_rest_route( $this->namespace, '/auth', [
			'methods'             => WP_REST_Server::CREATABLE,
			'callback'            => [ $this, 'auth' ],
			'permission_callback' => array( $this, 'plugin_permissions_check' ),
		] );

		register_rest_route( $this->namespace, '/auth/revoke_api_key', [
			'methods'             => WP_REST_Server::CREATABLE,
			'callback'            => [ $this, 'revoke_api_key' ],
			'permission_callback' => [ $this, 'permissions_check' ],
		] );
		register_rest_route( $this->namespace, '/auth/sync', [
			'methods'             => WP_REST_Server::CREATABLE,
			'callback'            => [ $this, 'sync_auth' ],
			'permission_callback' => [ $this, 'permissions_check' ],
		] );
		register_rest_route(
			$this->namespace, '/auth/get_product_sku', array(
				'methods'             => WP_REST_Server::CREATABLE,
				'callback'            => array( $this, 'get_product_sku_auth' ),
				'permission_callback' => array( $this, 'permissions_check_read_product' ),
			)
		);
	}
	public function plugin_permissions_check(){
		if (!empty(TMDS_DATA::get_instance()->get_params('enable'))){
			return true;
		}
		return false;
	}
	/**
	 * @param $request WP_REST_Request
	 *
	 * @return bool|WP_Error
	 */
	public function permissions_check_read_product( $request ) {
		$product_ids = $request->get_param( 'product_ids' );
		$product_id  = isset( $product_ids['id'] ) ? wc_clean( wp_unslash( $product_ids['id'] ) ) : '';
		if ( ! apply_filters( 'villatheme_'.$this->namespace.'_rest_check_product_read_permission', wc_rest_check_post_permissions( 'product', 'read', $product_id ) ) ) {
			return new WP_Error( 'woocommerce_rest_cannot_read', esc_html__( 'Unauthorized', 'tmds-dropshipping-for-temu-and-woo' ), array( 'status' => rest_authorization_required_code() ) );
		}
		return true;
	}
	public function permissions_check() {
		if ( ! apply_filters( 'villatheme_'.$this->namespace.'_rest_check_product_create_permission', wc_rest_check_post_permissions( 'product', 'create' ) ) ) {
			return new \WP_Error( 'woocommerce_rest_cannot_create', esc_html__( 'Unauthorized', 'tmds-dropshipping-for-temu-and-woo' ),
				array( 'status' => rest_authorization_required_code() ) );
		}

		return true;
	}
	public function check_plugin_active( \WP_REST_Request $request ) {
		wp_send_json(['status' => 'success']);
	}
	public function auth( \WP_REST_Request $request ) {
		$consumer_key    = sanitize_text_field( $request->get_param( 'consumer_key' ) );
		$consumer_secret = sanitize_text_field( $request->get_param( 'consumer_secret' ) );
		if ( $consumer_key && $consumer_secret ) {
			update_option( 'villatheme_'.$this->namespace.'_temp_api_credentials', $request->get_params() );
		}
	}
	public function revoke_api_key( \WP_REST_Request $request ) {
		$consumer_key    = sanitize_text_field( $request->get_param( 'consumer_key' ) );
		$consumer_secret = sanitize_text_field( $request->get_param( 'consumer_secret' ) );

		if ( ! $consumer_key ) {
			$authorization = $request->get_header( 'authorization' );
			if ( $authorization ) {
				$authorization = base64_decode( substr( $authorization, 6 ) );// phpcs:ignore WordPress.PHP.DiscouragedPHPFunctions.obfuscation_base64_decode
				$consumer      = explode( ':', $authorization );
				if ( count( $consumer ) === 2 ) {
					$consumer_key    = $consumer[0];
					$consumer_secret = $consumer[1];
				}
			}
		}

		if ( ! $consumer_key && ! empty( $_SERVER['PHP_AUTH_USER'] ) ) {
			$consumer_key = sanitize_text_field( wp_unslash( $_SERVER['PHP_AUTH_USER'] ) );
		}

		if ( ! $consumer_secret && ! empty( $_SERVER['PHP_AUTH_PW'] ) ) {
			$consumer_secret = sanitize_text_field( wp_unslash( $_SERVER['PHP_AUTH_PW'] ) );
		}

		wp_send_json(
			array(
				'status' => 'success',
				'result' => $this->revoke_woocommerce_api_key( $consumer_key, $consumer_secret ),
			)
		);
	}
	public function revoke_woocommerce_api_key( $consumer_key, $consumer_secret ) {
		global $wpdb;
		$consumer_key = wc_api_hash( sanitize_text_field( $consumer_key ) );
		$query        = "DELETE FROM %i WHERE consumer_key = %s AND consumer_secret=%s";

		return $wpdb->query( $wpdb->prepare( $query, ["{$wpdb->prefix}woocommerce_api_keys",$consumer_key, $consumer_secret ]) );// phpcs:ignore WordPress.DB.PreparedSQL.NotPrepared, WordPress.DB.DirectDatabaseQuery.DirectQuery, WordPress.DB.DirectDatabaseQuery.NoCaching
	}
	public function sync_auth( \WP_REST_Request $request ) {
		$this->validate( $request );
		$this->import_to_list( $request );
	}
	public function import_to_list( \WP_REST_Request $request ) {
		$result = array(
			'status'       => 'error',
			'message_type' => 1,
		);
		TMDS_DATA::villatheme_set_time_limit();
		$data             = $request->get_param( 'product_data' );
		$product_data = $this->parse_product_data( $data );
		if (empty($product_data) || empty($product_data['sku']) || empty($product_data['url'])){
			$result['message'] = esc_html__( 'No product data was sent', 'tmds-dropshipping-for-temu-and-woo' );
			wp_send_json( $result );
		}
		$currency  = $product_data['import_info']['currency_code'] ?? '';
		$import_currency_rate = self::$settings->get_params('import_currency_rate');
		if (!$currency || empty($import_currency_rate[$currency])){
			$result['message'] = esc_html__( 'There are no exchange rates to convert from this currency on Temu to your store currency when adding products to the import list. Please set the exchange rate before the import.', 'tmds-dropshipping-for-temu-and-woo' );
			wp_send_json( $result );
		}
		$sku                  = sanitize_text_field( wp_unslash($product_data['sku']) ) ;
		$post_id              = TMDS_Post::get_post_id_by_temu_id( $sku );
		if ( ! $post_id ) {
			$parent = '';
			$status = 'draft';
			$post_id = $this->create_import_product( $product_data, [ 'post_parent' => $parent, 'post_status' => $status ] );

			if ( is_wp_error( $post_id ) ) {
				$result['message'] = $post_id->get_error_message();
				wp_send_json( $result );
			} elseif ( ! $post_id ) {
				$result['message'] = esc_html__( 'Can not create post', 'tmds-dropshipping-for-temu-and-woo' );
				wp_send_json( $result );
			}

			$result['status']  = 'success';
			$result['message'] = esc_html__( 'Product is added to import list', 'tmds-dropshipping-for-temu-and-woo' );
		} else {
			$result['message'] = esc_html__( 'Product exists', 'tmds-dropshipping-for-temu-and-woo' );
		}
		wp_send_json( $result );
	}
	public function create_import_product($data, $post_data = []){
		$prefix = self::$settings::$prefix;
		$sku               = isset( $data['sku'] ) ? sanitize_text_field( $data['sku'] ) : '';
		$title             = isset( $data['name'] ) ? sanitize_text_field( $data['name'] ) : '';
		$description       = isset( $data['description'] ) ? wp_kses( stripslashes( $data['description'] ) ,TMDS_DATA::filter_allowed_html()) : '';
		$short_description = isset( $data['short_description'] ) ? wp_kses( stripslashes( $data['short_description'] ) ,TMDS_DATA::filter_allowed_html()) : '';
		$gallery           = isset( $data['gallery'] ) ? stripslashes_deep( $data['gallery'] ) : array();
		$variations        = isset( $data['variations'] ) ? stripslashes_deep( $data['variations'] ) : array();
		$attributes        = isset( $data['attributes'] ) ? stripslashes_deep( $data['attributes'] ) : array();
		$store_info        = isset( $data['store_info'] ) ? stripslashes_deep( $data['store_info'] ) : array();
		$variation_images  = isset( $data['variation_images'] ) ? stripslashes_deep( $data['variation_images'] ) : array();
		$import_info           = isset( $data['import_info'] ) ? stripslashes_deep( $data['import_info'] ) : [];
		$desc_images = [];
		if ( $description ) {
			preg_match_all( '/src="([\s\S]*?)"/im', $description, $matches );
			if ( isset( $matches[1] ) && is_array( $matches[1] ) && count( $matches[1] ) ) {
				$desc_images = array_values( array_unique( $matches[1] ) );
			}
		}
		$post_data = array_merge( [
			'post_title'   => $title,
			'post_type'    => "{$prefix}_draft_product",
			'post_status'  => 'draft',
			'post_excerpt' => '',
		], $post_data );
		$post_id    = TMDS_Post::insert_post( $post_data, true );
		if ( $post_id && ! is_wp_error( $post_id ) ) {
			if ( ! empty( $desc_images ) ) {
				TMDS_Post::update_post_meta( $post_id, "_{$prefix}_description_images", $desc_images );
			}
			TMDS_Post::update_post_meta( $post_id, "_{$prefix}_short_description", $short_description );
			TMDS_Post::update_post_meta( $post_id, "_{$prefix}_description", $description );
			TMDS_Post::update_post_meta( $post_id, "_{$prefix}_sku", $sku );
			TMDS_Post::update_post_meta( $post_id, "_{$prefix}_attributes", $attributes );
			TMDS_Post::update_post_meta( $post_id, "_{$prefix}_variation_images", $variation_images );
			TMDS_Post::update_post_meta( $post_id, "_{$prefix}_import_info", $import_info );

			$gallery = array_unique( array_filter( $gallery ) );
			if ( ! empty( $gallery ) ) {
				TMDS_Post::update_post_meta( $post_id, "_{$prefix}_gallery", $gallery );
			}
			if ( is_array( $store_info ) && !empty( $store_info ) ) {
				TMDS_Post::update_post_meta( $post_id, "_{$prefix}_store_info", $store_info );
			}
			if ( ! empty( $variations ) && is_array( $variations ) ) {
				TMDS_Post::update_post_meta( $post_id, "_{$prefix}_variations", $variations );
			}
			if ( !empty($data['url']) ) {
				TMDS_Post::update_post_meta( $post_id, "_{$prefix}_url", $data['url'] );
			}
		}
		return $post_id;
	}
	public function parse_product_data($data){
		$new_data = [
			'is_online'         => 1,
			'sku'               => $data['product_id'] ?? '',
			'url'               => $data['product_url'] ?? '',
			'name'              => $data['product_name'] ?? '',
			'description'       => $data['product_desc'] ?? '',
			'short_description' => '',
			'item_specifics'    => $data['product_specifics'] ?? '',
			'store_info'        => $data['store_info'] ??[],
			'video'             => '',
			'gallery'           => [],
			'attributes'        => [],
			'variations'        => [],
			'variation_images'  => [],
			'import_info'           => [
				'temu_attributes' => $data['temu_attributes'] ??[],
				'temu_variations' => $data['temu_variations'] ?? [],
				'language_code' => $data['language_code'] ?? '',
				'language_name' => $data['language_name'] ?? '',
				'region_code' => $data['region_code'] ?? '',
				'region_name' => $data['region_name'] ?? '',
				'currency_code' => $data['currency_code'] ?? '',
				'currency_symbol' => $data['currency_symbol'] ?? '',
				'variation_ids' => $data['variation_ids'] ?? [],
			],
		];
		$tmds_img = $src_attributes = $variations = $new_variation_images = [];
		if (is_array($new_data['item_specifics'])){
			$short_description=[];
			foreach ($new_data['item_specifics'] as $tmp){
				if (!empty($tmp['key']) && !empty($tmp['values'])){
					$tmp_value = is_array($tmp['values']) ? implode(', ', $tmp['values']) : $tmp['values'];
					$short_description[] = "<p>{$tmp['key']}: {$tmp_value}</p>";
				}
			}
			$new_data['short_description'] = implode('',$short_description);
		}
		if (!empty($data['product_gallery']) && is_array($data['product_gallery'])){
			$tmds_img = $data['product_gallery'];
			$new_data['gallery'] = $this->prepare_gallery( $tmds_img);
		}
		if (!empty($data['product_attributes']) && is_array($data['product_attributes']) && !empty($data['product_variations']) && is_array($data['product_variations'])){
			$src_attributes = $data['product_attributes'];
			$product_variations = $data['product_variations'];
			foreach ($src_attributes as $i => $prop){
				if (empty($prop['title']) || empty($prop['values']) || !is_array($prop['values'])){
					continue;
				}
				$slug      = wc_sanitize_taxonomy_name( $prop['title']  );
				$src_attributes[ $i ]['slug'] = $slug;
				$src_attributes[ $i ]['default_values'] = $prop['values'];
			}
			foreach ( $product_variations as $item ) {
				if (empty($item['product_sku'])){
					continue;
				}
				$sku_id                     = $item['product_sku'];
				$variation = [];
				$variation['sku']           = $sku_id;
				$variation['skuId']         = $sku_id;
				$variation['regular_price'] = $item['product_regular_price'] ?? '';
				$variation['sale_price'] = $item['product_sale_price'] ?? '';
				$variation['is_on_sale'] = $item['product_is_on_sale'] ?? '';
				$variation['sale_price_html'] = $item['product_sale_price_html'] ?? '';
				$variation['regular_price_html'] = $item['product_regular_price_html'] ?? '';
				$variation['stock']         = $item['product_stock'] ?? '';
				$variation['limit_qty']         = $item['product_limit_qty'] ?? '';
				$variation['attributes'] = $item['product_attributes'] ??[];
				if (!empty($item['product_img_url'])){
					$image                  =  $this->prepare_gallery($item['product_img_url']);
					$variation['image']     = $image;
					$new_variation_images[] = $image;
				}
				$variation['shipping_cost'] = 0;
				$variations[] = $variation;
			}
		}else{
			$variation = [];
			$variation['skuId']         = $new_data['product_sku'] ??'';
			$variation['regular_price'] = $data['product_regular_price'] ?? '';
			$variation['sale_price'] = $data['product_sale_price'] ?? '';
			$variation['sale_price_html'] = $data['product_sale_price_html'] ?? '';
			$variation['regular_price_html'] = $data['product_regular_price_html'] ?? '';
			$variation['is_on_sale'] = $data['product_is_on_sale'] ?? '';
			$variation['stock']         = $data['product_stock'] ?? '';
			$variation['limit_qty']         = $data['product_limit_qty'] ?? '';
			$variation['shipping_cost'] = 0;
			$variations[] = $variation;
		}
		if (!empty($src_attributes) ) {
			$new_data['attributes']       = $src_attributes;
		}
		if (!empty($variations)) {
			$new_data['variations']       = $variations;
			$new_data['variation_images'] = array_values($new_variation_images);
		}
		$new_data['import_info']['images'] = $tmds_img;
		return $new_data;
	}
	public function prepare_gallery( $gallery ) {
		if ( ! empty( $gallery ) && is_array( $gallery ) ) {
			foreach ( $gallery as $key => $img ) {
				$gallery[ $key ] = $this->prepare_gallery($img['url'] ?? $img);
			}
		}elseif ( 'https' !== substr( $gallery, 0, 5 ) ) {
			$gallery = set_url_scheme( $gallery, 'https' );
		}
		return $gallery;
	}
	/**
	 * @param $request WP_REST_Request
	 */
	public function get_product_sku_auth( $request ) {
		$this->validate( $request );
		$this->get_product_sku( $request );
	}
	/**
	 * @param $request WP_REST_Request
	 */
	public function get_product_sku( $request ) {
		$result   = array(
			'status'  => 'success',
			'message' => '',
			'data'    => wp_json_encode( array() ),
		);
		$products = $request->get_param( 'products' );
		if ( $products && is_array( $products ) ) {
			TMDS_DATA::villatheme_set_time_limit();
			$args      = array(
				'tmds_query'          => 1,
				'post_type'      => 'tmds_draft_product',
				'posts_per_page' => count( $products ),
				'orderby'        => 'meta_value_num',
				'order'          => 'ASC',
				'post_status'    => array(
					'publish',
					'draft',
					'trash',
					'override'
				),
				'fields'         => 'ids',
				'meta_query'     => array( // phpcs:ignore WordPress.DB.SlowDBQuery.slow_db_query_meta_query
					'relation' => 'AND',
					array(
						'key'     => '_tmds_sku',
						'value'   => $products,
						'compare' => 'IN'
					)
				)
			);
			$the_query   = TMDS_Post::query( $args );
			$post_ids = $the_query->get_posts();
			wp_reset_postdata();
			$imported  = array();
			if ( !empty($post_ids) ) {
				foreach ( $post_ids as $id ) {
					$imported[] = TMDS_Post::get_post_meta( $id, '_tmds_sku', true );
				}
				$result['data'] = wp_json_encode( $imported );
			}
		}
		wp_send_json( $result );
	}
	public function validate( \WP_REST_Request $request ) {
		$result = array(
			'status'       => 'error',
			'message'      => '',
			'message_type' => 1,
		);

		/*check ssl*/
		if ( ! is_ssl() ) {
			$result['message']      = esc_html__( 'SSL is required', 'tmds-dropshipping-for-temu-and-woo' );
			$result['message_type'] = 2;

			wp_send_json( $result );
		}

		/*check enable*/
		if ( ! self::$settings->get_params( 'enable' ) ) {
			/* translators: %s */
			$result['message']      = sprintf( esc_html__( '%s plugin is currently disabled. Please enable it to use this function.', 'tmds-dropshipping-for-temu-and-woo' ),
				TMDS_NAME );
			$result['message_type'] = 2;

			wp_send_json( $result );
		}
	}
}