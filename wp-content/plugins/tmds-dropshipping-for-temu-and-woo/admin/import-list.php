<?php

defined( 'ABSPATH' ) || exit;

class TMDS_Admin_Import_List {
	protected static $settings, $prefix;
	private static $variations_count;
	public static $process_image;

	public function __construct() {
		self::$variations_count = 0;
		self::$settings         = TMDS_DATA::get_instance();
		self::$prefix           = self::$settings::$prefix;
		add_action( 'init', array( $this, 'background_process' ) );
		add_action( 'admin_init', array( $this, 'move_queued_images' ) );
		add_action( 'admin_init', array( $this, 'empty_import_list' ) );
		add_action( 'admin_head', array( $this, 'menu_product_count' ), 999 );
		add_action( 'admin_notices', array( $this, 'admin_notices' ) );
		add_action( 'wc_marketplace_suggestions_products_empty_state', array( $this, 'marketplace_suggestions_products_empty_state' ) );
		add_filter( 'tmds_admin_ajax_events', [ $this, 'ajax_events' ], 10, 2 );
	}

	public function ajax_events( $events, $prefix ) {
		if ( ! is_array( $events ) ) {
			$events = [];
		}
		$events += [
			$prefix . '_import'                => array(
				'function' => 'import',
				'class'    => $this,
			),
			$prefix . '_remove'                => array(
				'function' => 'remove',
				'class'    => $this,
			),
			$prefix . '_save_attributes'       => array(
				'function' => 'save_attributes',
				'class'    => $this,
			),
			$prefix . '_remove_attribute'      => array(
				'function' => 'remove_attribute',
				'class'    => $this,
			),
			$prefix . '_load_variations_table' => array(
				'function' => 'load_variations_table',
				'class'    => $this,
			),
		];
		return $events;
	}

	public static function import() {
		$response = array(
			'status'         => 'error',
			'woo_product_id' => '',
			'button_html'    => '',
		);
		$action = 'admin_ajax';
		if ( apply_filters( 'tmds_verify_ajax_nonce', true, $action) &&
		     !check_ajax_referer( 'tmds_' . $action, 'tmds_nonce', false )) {
			$result['message']='Invalid nonce';
			wp_send_json($result);
		}
		if ( ! current_user_can( apply_filters( 'villatheme_tmds_admin_sub_menu_capability', 'manage_woocommerce', 'tmds-import-list' ) ) ) {
			$result['message']='Missing role';
			wp_send_json($result);
		}
		TMDS_DATA::villatheme_set_time_limit();
		if ( ! isset( $_POST['form_data']['z_check_max_input_vars'] ) ) {
			/*z_check_max_input_vars is the last key of POST data. If it does not exist in $form_data after using parse_str(), some data may also be missing*/
			$response['message'] = esc_html__( 'PHP max_input_vars is too low, please increase it in php.ini', 'tmds-dropshipping-for-temu-and-woo' );
			wp_send_json( $response );
		}
		$data = isset( $_POST['form_data']['tmds_product'] ) ? (array) self::$settings::json_decode( wc_clean( wp_unslash( $_POST['form_data']['tmds_product'] ) ) ) : [];// phpcs:ignore WordPress.Security.ValidatedSanitizedInput.InputNotSanitized

        if ( empty( $data ) ) {
			$response['message'] = esc_html__( 'Please select product to import', 'tmds-dropshipping-for-temu-and-woo' );
			wp_send_json( $response );
		}
		$selected                    = isset( $_POST['selected'] ) ? array_map( 'TMDS_DATA::json_decode', (array) self::$settings::json_decode( wc_clean( wp_unslash( $_POST['selected'] ) ) ) ) : [];// phpcs:ignore WordPress.Security.ValidatedSanitizedInput.InputNotSanitized
		$product_data                = array_values( $data )[0];
		$product_draft_id            = array_keys( $data )[0];
		$product_data['description'] = isset( $_POST['form_data'][ 'description_' . $product_draft_id ] ) ? wp_kses_post( wp_unslash( $_POST['form_data'][ 'description_' . $product_draft_id ] ) ) : '';

		if ( ! isset( $selected[ $product_draft_id ] ) || ! is_array( $selected[ $product_draft_id ] ) || empty( $selected[ $product_draft_id ] ) ) {
			$response['message'] = esc_html__( 'Please select at least 1 variation to import this product.', 'tmds-dropshipping-for-temu-and-woo' );
			wp_send_json( $response );
		}

		if ( ! $product_draft_id || self::$settings::sku_exists( $product_data['sku'] ) ) {
			$response['message'] = esc_html__( 'Sku exists.', 'tmds-dropshipping-for-temu-and-woo' );
			wp_send_json( $response );
		}

		$prefix = self::$prefix;
		if ( TMDS_Post::get_post_id_by_temu_id( TMDS_Post::get_post_meta( $product_draft_id, "_{$prefix}_sku", true ), [ 'publish' ] ) ) {
			wp_send_json( array(
				'status'  => 'error',
				'message' => esc_html__( 'This product has already been imported', 'tmds-dropshipping-for-temu-and-woo' ),
			) );
		}
		$import_info           = TMDS_Post::get_post_meta( $product_draft_id, "_{$prefix}_import_info", true );
		$currency              = $import_info['currency_code'] ?? $import_info['temu_locale_settings']['currency']['code'] ?? get_woocommerce_currency();
		$variations_attributes = [];
		$attributes            = self::get_product_attributes( $product_draft_id );
		if ( isset( $product_data['variations'] ) ) {
			$variations = array_values( $product_data['variations'] );
			if ( ! empty( $variations ) ) {
				$var_default = isset( $product_data['default_variation'] ) ? $product_data['default_variation'] : '';

				foreach ( $variations as $variations_k => $variations_v ) {
					$variations_attribute = isset( $variations_v['attributes'] ) ? $variations_v['attributes'] : [];
					if ( $var_default === $variations_v['skuId'] ) {
						$product_data['variation_default'] = $variations_attribute;
					}

					if ( is_array( $variations_attribute ) && !empty( $variations_attribute ) ) {
						foreach ( $variations_attribute as $variations_attribute_k => $variations_attribute_v ) {
							if ( ! isset( $variations_attributes[ $variations_attribute_k ] ) ) {
								$variations_attributes[ $variations_attribute_k ] = [$variations_attribute_v];
							} elseif ( ! in_array( $variations_attribute_v, $variations_attributes[ $variations_attribute_k ] ) ) {
								$variations_attributes[ $variations_attribute_k ][] = $variations_attribute_v;
							}
							if (is_array($variations_attribute_v)){
								foreach ($variations_attribute_v as $k => $v){
									$variations_attribute[$variations_attribute_k] = [
										'id' => $k,
										'title' => $v,
									];
								}
							}
						}
					}
					$variations[$variations_k]['attributes'] = $variations_attribute;
				}

				if ( is_array( $attributes ) && ! empty( $attributes ) ) {
					foreach ( $attributes as $attributes_k => $attributes_v ) {
                        if (empty($attributes_v['set_variation'])){
                            unset($attributes[$attributes_k]);
                        }
						if ( ! empty( $variations_attributes[ $attributes_k] ) ) {
                            $tmp = [];
                            foreach ($variations_attributes[$attributes_k] as $variations_attribute_v){
                                if (is_array($variations_attribute_v)){
                                    foreach ($variations_attribute_v as $k => $v){
                                        $tmp[] = [
                                                'id' => $k,
                                                'title' => $v,
                                        ];
                                    }
                                }
                            }
							$attributes[ $attributes_k ]['values'] =  $tmp;
						}
					}
				}
			}
		} else {
			$variations = self::get_product_variations( $product_draft_id );
			foreach ( $variations as $variations_k => $variations_v ) {
				$variation_sale_price                         = self::$settings::string_to_float( $variations_v['sale_price'] );
				$variation_regular_price                      = self::$settings::string_to_float( $variations_v['regular_price'] );
				$price                                        = $variation_sale_price ?: $variation_regular_price;
				$price                                        = self::$settings->process_exchange_price( $price, $currency );
				$variations[ $variations_k ]['regular_price'] = self::$settings->process_price( $price );
				$variations[ $variations_k ]['sale_price']    = self::$settings->process_price( $price, true );
			}
			if ( is_array( $attributes ) && ! empty( $attributes ) ) {
				foreach ( $attributes as $attributes_k => $attributes_v ) {
					if (empty($attributes_v['set_variation'])){
						unset($attributes[$attributes_k]);
					}
				}
			}
		}
		if ( ! empty( $variations ) ) {
			$product_data['gallery'] = array_values( array_filter( $product_data['gallery'] ?? [] ) );

			if ( ! empty( $product_data['image'] ) ) {
				$product_image_key = array_search( $product_data['image'], $product_data['gallery'] );
				if ( $product_image_key !== false ) {
					unset( $product_data['gallery'][ $product_image_key ] );
					$product_data['gallery'] = array_values( $product_data['gallery'] );
				}
			}

			$gallery = TMDS_Post::get_post_meta( $product_draft_id, '_' . $prefix . '_gallery', true );
			$desc_images      = TMDS_Post::get_post_meta( $product_draft_id, '_' . $prefix . '_description_images', true );
			$variation_images = TMDS_Post::get_post_meta( $product_draft_id, '_' . $prefix . '_variation_images', true );
			if ( ! $gallery || ! is_array( $gallery ) ) {
				$gallery = [];
			}
			if ( ! $variation_images || ! is_array( $variation_images ) ) {
				$variation_images = [];
			}
			if ( ! $desc_images || ! is_array( $desc_images ) ) {
				$desc_images = [];
			}
			$variation_images = array_unique( array_merge( $gallery, $desc_images, $variation_images ) );
			$product_data['attributes']              = $attributes;
			$product_data['variation_images']        = $variation_images;
			$product_data['variations']              = $variations;
			$product_data['parent_id']               = $product_draft_id;
			$product_data[ $prefix . '_product_id' ] = TMDS_Post::get_post_meta( $product_draft_id, "_{$prefix}_sku", true );
			$woo_product_id                          = self::import_product( $product_data );
			if ( is_wp_error( $woo_product_id ) ) {
				$response['message'] = $woo_product_id->get_error_messages();
			} elseif (!$woo_product_id) {
				$response['message'] = esc_html__( 'Can not create product', 'tmds-dropshipping-for-temu-and-woo' );
				$response['woo_product_id'] = $woo_product_id;
			}else{
				$response['status']         = 'success';
				$response['message']        = esc_html__( 'Import successfully', 'tmds-dropshipping-for-temu-and-woo' );
				$response['woo_product_id'] = $woo_product_id;

				$response['button_html'] = self::get_button_view_edit_html( $woo_product_id );
            }
		} else {
			$response['message'] = esc_html__( 'Please select at least 1 variation to import this product.', 'tmds-dropshipping-for-temu-and-woo' );
		}
		wp_send_json( $response );
	}

	/**
	 * Import a product from Import list
	 *
	 * @param $product_data
	 *
	 * @return int|WP_Error
	 * @throws Exception
	 */
	public static function import_product( $product_data ) {
		$prefix       = self::$prefix;
		$product_data = apply_filters( 'villatheme_'. $prefix . '_import_list_product_data', $product_data, $product_data );
		do_action( 'villatheme_'. $prefix . '_import_list_before_import', $product_data );
		TMDS_DATA::villatheme_set_time_limit();
		$tmds_product_id        = $product_data[ $prefix . '_product_id' ];
		$parent_id                  = $product_data['parent_id'];
		$image                      = $product_data['image'];
		$categories                 = $product_data['categories'] ?? [];
		$shipping_class             = $product_data['shipping_class'] ?? '';
		$title                      = $product_data['title'];
		$sku                        = $product_data['sku'] ?? '';
		$status                     = $product_data['status'];
		$tags                       = $product_data['tags'] ?? [];
		$description                = $product_data['description'];
		$variations                 = $product_data['variations'];
		$gallery                    = $product_data['gallery'];
		$attributes                 = $product_data['attributes'];
		$catalog_visibility         = $product_data['catalog_visibility'];
		$default_attr               = $product_data['variation_default'] ?? [];
        $manage_stock = self::$settings->get_params( 'manage_stock' );
		$disable_background_process = self::$settings->get_params( 'disable_background_process' );
        $create_as_wc_variable = is_array( $attributes ) && !empty( $attributes ) && ( count( $variations ) > 1 || ! self::$settings->get_params( 'simple_if_one_variation' ) );
		if ( $create_as_wc_variable ) {
			$wc_product = new WC_Product_Variable();
		}else{
			$wc_product = new WC_Product_Simple();
		}
		$wc_product->set_name($title);
		$wc_product->set_description( $description );
		$wc_product->set_status($status);
		$wc_product->set_sku($sku);
		/*Set product categories*/
		if ( is_array( $categories ) && !empty( $categories ) ) {
			$wc_product->set_category_ids($categories);
		}
		/*Set product shipping class*/
		if ( $shipping_class && get_term_by( 'id', $shipping_class, 'product_shipping_class' ) ) {
			$wc_product->set_shipping_class_id($shipping_class);
		}
		$wc_product->set_catalog_visibility($catalog_visibility ?:'visible');
		$product_id = $wc_product->save();
		$wc_product = wc_get_product($product_id);
		if (!$wc_product){
			return $product_id;
		}
		if ( $parent_id ) {
			$update_data = array(
				'ID'          => $parent_id,
				'post_status' => 'publish',
				'post_author' => get_current_user_id(),
			);
			TMDS_Post::update_post( $update_data );
			TMDS_Post::update_post_meta( $parent_id, '_' . $prefix . '_woo_id', $product_id );
		}
		$wc_product->add_meta_data( '_' . $prefix . '_product_id', $tmds_product_id, true);
		/*Set product tag*/
		if ( is_array( $tags ) && !empty( $tags ) ) {
            $wc_product->set_tag_ids($tags);
		}
		/*download image gallery*/
		$dispatch = false;
		if ( isset( $product_data['old_product_image'] ) ) {
			if ( $product_data['old_product_image'] ) {
				$wc_product->set_image_id($product_data['old_product_image']);
			}
			if ( isset( $product_data['old_product_gallery'] ) && $product_data['old_product_gallery'] ) {
				$wc_product->set_gallery_image_ids( $product_data['old_product_gallery']);
			}
		} else {
			if ( $image ) {
				$thumb_id = self::$settings::download_image( $image_id, $image, $product_id );
				if ( ! is_wp_error( $thumb_id ) ) {
					$wc_product->set_image_id($thumb_id);
				}
			}
			self::process_gallery_images( $gallery, $disable_background_process, $product_id, $parent_id, $dispatch );
		}
		self::process_description_images( $description, $disable_background_process, $product_id, $parent_id, $dispatch );
		if ( $create_as_wc_variable ) {
			$attr_data = self::create_product_attributes( $attributes, $default_attr, $attributes_info );
			if ( ! empty( $attr_data ) ) {
				$wc_product->set_attributes( $attr_data );
				if ( $default_attr ) {
					$wc_product->set_default_attributes( $default_attr );
				}
				$wc_product->save_meta_data();
				$wc_product->save();
			}
			/*Create product variation*/
			self::import_product_variation( $product_id, $product_data, $attributes_info, $dispatch, $disable_background_process );
		}else{
			if ( $dispatch ) {
				self::$process_image->save()->dispatch();
			}
			$sale_price    = isset( $variations[0]['sale_price'] ) ? floatval( $variations[0]['sale_price'] ) : '';
			$regular_price = isset( $variations[0]['regular_price'] ) ? floatval( $variations[0]['regular_price'] ) : 0;
			if ( $sale_price ) {
				$wc_product->set_price($sale_price);
				$wc_product->set_sale_price($sale_price);
			}else{
				$wc_product->set_price($regular_price);
				$wc_product->set_regular_price($regular_price);
			}
			$wc_product->set_manage_stock($manage_stock ? 'yes' : 'no');
			$wc_product->set_stock_status('instock');
			if ( ! empty( $variations[0]['stock'] ) && $manage_stock) {
				$wc_product->set_stock_quantity(absint( $variations[0]['stock'] ));
			}
			$wc_product->add_meta_data( '_' . $prefix . '_variation_id', $variations[0]['skuId'] , true);
		}
		if ( ! empty( $product_data['custom_pd_options'][0] ) ) {
			$custom_pd_options = $product_data['custom_pd_options'][0];
			$epow_form_data = [
				[
					'sectionId' => 'section_' . current_time( 'timestamp' ),
					'label'     => '',
					'enable'    => 1,
					'elements'  => [
						[
							'enable' => 1,
							'elementId' => 'tmds_personalization_field',
							'label' => $custom_pd_options['label'] ?? '',
							'description' => $custom_pd_options['desc'] ?? '',
							'maxlength' => $custom_pd_options['maxlength'] ?? '',
							'max' => $custom_pd_options['maxlength'] ?? '',
							'type' => $custom_pd_options['type'] ?? 'textarea',
							'required' => $custom_pd_options['required'] ?? '',
						]
					],
				]
			];
			$wc_product->update_meta_data( 'epow_form_data', $epow_form_data );
		}
		$wc_product->save_meta_data();
		$wc_product->save();
		return $product_id;
	}

	public static function import_product_variation( $product_id, $product_data, $attributes_info, $dispatch, $disable_background_process ) {
		$product = wc_get_product( $product_id );
		if ( ! $product ) {
			return;
		}
		$prefix = self::$prefix;
		if ( is_array( $product_data['variations'] ) && ! empty( $product_data['variations'] ) ) {
			$variation_ids = [];
			$use_global_attributes = self::$settings->get_params( 'use_global_attributes' );
			$manage_stock          = self::$settings->get_params( 'manage_stock' ) ? 'yes' : 'no';
			foreach ( $product_data['variations'] as $pos =>$product_variation ) {
				if ( ! empty( $product_variation['variation_id'] ) ) {
					$variation_id = $product_variation['variation_id'];
				} else {
					$stock_quantity = isset( $product_variation['stock'] ) ? absint( $product_variation['stock'] ) : 0;
					$variation      = new \WC_Product_Variation();
					$variation->set_parent_id( $product_id );
					$attributes = [];
                    $get_attr_value = $use_global_attributes ? 'slug' : 'title';
					foreach ( $product_variation['attributes'] as $option_k => $attr ) {
						if ( ! isset( $attributes_info[ $option_k ]['slug'], $attributes_info[ $option_k ]['values'] ) || ! isset( $attr['id'] ) ) {
							continue;
						}
						$options_v = $attributes_info[ $option_k ]['values'];
						if ( is_array( $options_v ) ) {
							foreach ( $options_v as $option_v ) {
								if ( isset( $option_v['id'], $option_v[$get_attr_value] ) && $option_v['id'] == $attr['id'] ) {
									$attributes[ $attributes_info[ $option_k ]['slug'] ] = $option_v[$get_attr_value];
									break;
								}
							}
						}
					}
					$variation->set_attributes( $attributes );
					$sale_price    = isset( $product_variation['sale_price'] ) && is_numeric( $product_variation['sale_price']) ? floatval( $product_variation['sale_price'] ) : '';
					$regular_price = isset( $product_variation['regular_price'] ) ? floatval( $product_variation['regular_price'] ) : '';

					/*Set metabox for variation . Check field name at woocommerce/includes/class-wc-ajax.php*/
					$fields = array(
						'sku'            => $product_variation['sku'] ? wc_product_generate_unique_sku( 0, $product_variation['sku'] ) : '',
						'regular_price'  => $regular_price,
						'price'          => $regular_price,
						'manage_stock'   => $manage_stock,
						'stock_status'   => 'instock',
						'stock_quantity' => $stock_quantity,
					);

					if (  is_numeric($sale_price) && $sale_price < $regular_price ) {
						$fields['sale_price'] = $sale_price;
						$fields['price']      = $sale_price;
					}

					foreach ( $fields as $field => $value ) {
						$variation->{"set_$field"}( wc_clean( $value ) );
					}

					do_action( 'tmds_product_variation_linked', $variation->save() );

					$variation_id = $variation->get_id();
                    $variation->add_meta_data( '_' . $prefix . '_variation_id', $product_variation['sku'] ?? '' ,true);
				}
				if ( $product_variation['image'] ?? '' ) {
					if ( in_array($product_variation['image'],$product_data['variation_images'])) {
						$variation_ids[ $pos ]= [
							'src' =>  $product_variation['image'],
							'variation_id' =>  [$variation_id],
						];
					}
				}
				$dispatch = apply_filters('tmds_dispatch_after_make_variation_data',$dispatch,$variation_id, $product_variation,$disable_background_process);
			}
			if ( ! empty( $variation_ids ) ) {
				if ( $disable_background_process ) {
					foreach ( $variation_ids as  $values ) {
						if ( !empty( $values ) && ! empty( $values['src'] ) ) {
							$image_data = array(
								'woo_product_id' => $product_id,
								'parent_id'      => '',
								'src'            => $values['src'],
								'product_ids'    => $values['variation_id'],
								'set_gallery'    => 0,
							);
							TMDS_Error_Images_Table::insert( $product_id, implode( ',', $image_data['product_ids'] ), $image_data['src'], intval( $image_data['set_gallery'] ) );
						}
					}
				} else {
					foreach ( $variation_ids as $key => $values ) {
						if ( !empty( $values ) && ! empty( $values['src']) ) {
							$dispatch   = true;
							$image_data = array(
								'woo_product_id' => $product_id,
								'parent_id'      => '',
								'src'            => $values['src'],
								'product_ids'    => $values['variation_id'],
								'set_gallery'    => 0,
							);
							self::$process_image->push_to_queue( $image_data );
						}
					}
				}
			}
		}
		$data_store = $product->get_data_store();
		$data_store->sort_all_product_variations( $product->get_id() );
		if ( $dispatch ) {
			self::$process_image->save()->dispatch();
		}
	}

	public static function get_term_by_name( $value, $taxonomy = '', $output = OBJECT, $filter = 'raw' ) {
		// 'term_taxonomy_id' lookups don't require taxonomy checks.
		if ( ! taxonomy_exists( $taxonomy ) ) {
			return false;
		}

		// No need to perform a query for empty 'slug' or 'name'.
		$value = (string) $value;

		if ( 0 === strlen( $value ) ) {
			return false;
		}

		$args = array(
			'get'                    => 'all',
			'name'                   => $value,
			'number'                 => 0,
			'taxonomy'               => $taxonomy,
			'update_term_meta_cache' => false,
			'orderby'                => 'none',
			'suppress_filter'        => true,
		);

		$terms = get_terms( $args );
		if ( is_wp_error( $terms ) || empty( $terms ) ) {
			return false;
		}
		$check_slug = sanitize_title( $value );
		if ( count( $terms ) > 1 ) {
			foreach ( $terms as $term ) {
				if ( $term->slug == $check_slug ) {
					return get_term( $term, $taxonomy, $output, $filter );
				}
				if ( $term->name === $value ) {
					return get_term( $term, $taxonomy, $output, $filter );
				}
			}
		}
		$term = array_shift( $terms );

		return get_term( $term, $taxonomy, $output, $filter );
	}

	public static function process_description_images( $description, $disable_background_process, $product_id, $parent_id, &$dispatch ) {
		if ( $description && ! self::$settings->get_params( 'use_external_image' ) && self::$settings->get_params( 'download_description_images' ) ) {
			preg_match_all( '/src="([\s\S]*?)"/im', $description, $matches );

			if ( isset( $matches[1] ) && is_array( $matches[1] ) && count( $matches[1] ) ) {
				$description_images = array_unique( $matches[1] );

				if ( $disable_background_process ) {
					foreach ( $description_images as $description_image ) {
						TMDS_Error_Images_Table::insert( $product_id, '', $description_image, 2 );
					}
				} else {
					foreach ( $description_images as $description_image ) {
						$images_data = array(
							'woo_product_id' => $product_id,
							'parent_id'      => $parent_id,
							'src'            => $description_image,
							'product_ids'    => [],
							'set_gallery'    => 2,
						);
						self::$process_image->push_to_queue( $images_data );
					}
					$dispatch = true;
				}
			}
		}
	}

	public static function process_gallery_images( $gallery, $disable_background_process, $product_id, $parent_id, &$dispatch ) {
		if ( is_array( $gallery ) && count( $gallery ) ) {
			if ( $disable_background_process ) {
				foreach ( $gallery as $image_url ) {
					$image_data = array(
						'woo_product_id' => $product_id,
						'parent_id'      => $parent_id,
						'src'            => $image_url,
						'product_ids'    => [],
						'set_gallery'    => 1,
					);
					TMDS_Error_Images_Table::insert( $product_id, implode( ',', $image_data['product_ids'] ), $image_data['src'], intval( $image_data['set_gallery'] ) );
				}
			} else {
				$dispatch = true;
				foreach ( $gallery as $image_url ) {
					$image_data = array(
						'woo_product_id' => $product_id,
						'parent_id'      => $parent_id,
						'src'            => $image_url,
						'product_ids'    => [],
						'set_gallery'    => 1,
					);
					self::$process_image->push_to_queue( $image_data );
				}
			}
		}
	}

	public static function create_product_attributes( $attributes, &$default_attr, &$attributes_info ) {
		global $wp_taxonomies;
		$position  = 0;
		$attr_data = [];
		if ( ! $attributes_info ) {
			$attributes_info = [];
		}
        $variation_visible = self::$settings->get_params( 'variation_visible' );
		if ( self::$settings->get_params( 'use_global_attributes' ) ) {
			foreach ( $attributes as $key => $attr ) {
				if ( ! isset( $attr['slug'] ) ) {
					$attr['slug'] = self::$settings::sanitize_taxonomy_name( $attr['title'] ?? $key );
				}
				$attributes_info[ $key ] = [
					'slug' => 'pa_'.$attr['slug'],
					'values' => []
				];
				$attribute_name                   = isset( $attr['title'] ) ? $attr['title'] : self::$settings::get_attribute_name_by_slug( $attr['slug'] );
				$attribute_id                     = wc_attribute_taxonomy_id_by_name( $attribute_name );
				if ( ! $attribute_id ) {
					$attribute_id = wc_create_attribute( array(
						'name'         => $attribute_name,
						'slug'         => $attr['slug'],
						'type'         => 'select',
						'order_by'     => 'menu_order',
						'has_archives' => false,
					) );
				}
				if ( $attribute_id && ! is_wp_error( $attribute_id ) ) {
					$attribute_obj     = wc_get_attribute( $attribute_id );
					$attribute_options = [];
					if ( ! empty( $attribute_obj ) ) {
						$taxonomy = $attribute_obj->slug; // phpcs:ignore
						if ( isset( $default_attr[ $key ] ) ) {
							$default_attr[ $taxonomy ] = $default_attr[$key ];
							unset( $default_attr[ $key ] );
						}
						/*Update global $wp_taxonomies for latter insert attribute values*/
						$wp_taxonomies[ $taxonomy ] = new \WP_Taxonomy( $taxonomy, 'product' );
						if ( ! empty( $attr['values'] ) ) {
							$attributes_info_values = [];
							foreach ( $attr['values'] as $attr_value ) {
                                if (empty($attr_value['title'])){
                                    continue;
                                }
                                $tmp = $attr_value;
								$attr_value  = strval( wc_clean( $attr_value['title'] ) );
								$insert_term = wp_insert_term( $attr_value, $taxonomy );
								if ( ! is_wp_error( $insert_term ) ) {
									$attribute_options[]                         = $insert_term['term_id'];
									$term_exists = get_term_by( 'id', $insert_term['term_id'], $taxonomy );
									if ( $term_exists ) {
										if ( isset( $default_attr[ $taxonomy ] ) ) {
											$default_attr[ $taxonomy ] = $term_exists->slug;
										}
                                        $tmp['slug'] = $term_exists->slug;
									}
								} elseif ( isset( $insert_term->error_data ) && isset( $insert_term->error_data['term_exists'] ) ) {
									$attribute_options[]                         = $insert_term->error_data['term_exists'];
									$term_exists = get_term_by( 'id', $insert_term->error_data['term_exists'], $taxonomy );
									if ( $term_exists ) {
										if ( isset( $default_attr[ $taxonomy ] ) ) {
											$default_attr[ $taxonomy ] = $term_exists->slug;
										}
										$tmp['slug'] = $term_exists->slug;
									}
								}
                                $attributes_info_values[] = $tmp;
							}
                            $attributes_info[$key]['values']= $attributes_info_values;
						}
					}
					$attribute_object = new \WC_Product_Attribute();
					$attribute_object->set_id( $attribute_id );
					$attribute_object->set_name( wc_attribute_taxonomy_name_by_id( $attribute_id ) );
					if ( !empty( $attribute_options ) ) {
						$attribute_object->set_options( $attribute_options );
					} else {
						$attribute_object->set_options( array_column($attr['values'],'title') );
					}
					$attribute_object->set_position( isset( $attr['position'] ) ? $attr['position'] : $position );
					$attribute_object->set_visible( $variation_visible ? 1 : '' );
					$attribute_object->set_variation( 1 );
					$attr_data[] = $attribute_object;
				}
				$position ++;
			}
		} else {
			foreach ( $attributes as $key => $attr ) {
				if ( ! isset( $attr['slug'] ) ) {
					$attr['slug'] = self::$settings::sanitize_taxonomy_name( $attr['title'] ?? $key );
				}
				$attribute_name   = isset( $attr['title'] ) ? $attr['title'] : self::$settings::get_attribute_name_by_slug( $attr['slug'] );
				$attribute_object = new \WC_Product_Attribute();
				$attribute_object->set_name( $attribute_name );
				$attribute_object->set_options( array_column($attr['values'],'title') );
				$attribute_object->set_position( isset( $attr['position'] ) ? $attr['position'] : $position );
				$attribute_object->set_visible( $variation_visible ? 1 : '' );
				$attribute_object->set_variation( 1 );
				$attr_data[]                      = $attribute_object;
				$attributes_info[ $key ] = [
                        'slug' => $attr['slug'],
                        'values' => array_values( $attr['values'] )
                ];
				$position ++;
			}
		}

		return $attr_data;
	}

	/**
	 * @param $woo_product_id
	 *
	 * @return false|string
	 */

	public static function get_button_view_edit_html( $woo_product_id ) {
		ob_start();
		?>
        <a href="<?php echo esc_url( get_post_permalink( $woo_product_id ) ) ?>"
           target="_blank" class="vi-ui mini button labeled icon"
           rel="nofollow"><i class="icon eye"></i><?php esc_html_e( 'View', 'tmds-dropshipping-for-temu-and-woo' ); ?></a>
        <a href="<?php echo esc_url( admin_url( "post.php?post={$woo_product_id}&action=edit" ) ) ?>"
           target="_blank" class="vi-ui mini button labeled icon primary"
           rel="nofollow"><i class="icon edit"></i><?php esc_html_e( 'Edit', 'tmds-dropshipping-for-temu-and-woo' ) ?></a>
		<?php
		return apply_filters( 'villatheme_'.self::$prefix . '_import_list_button_view_edit_html', ob_get_clean(), $woo_product_id );
	}

	public static function remove() {
		$result     = array(
			'status'  => 'error',
			'message' => esc_html__( 'Not found', 'tmds-dropshipping-for-temu-and-woo' ),
		);
		$action = 'admin_ajax';
		if ( apply_filters( 'tmds_verify_ajax_nonce', true, $action) &&
		     !check_ajax_referer( 'tmds_' . $action, 'tmds_nonce', false )) {
			$result['message']='Invalid nonce';
			wp_send_json($result);
		}
		if ( ! current_user_can( apply_filters( 'villatheme_tmds_admin_sub_menu_capability', 'manage_woocommerce', 'tmds-import-list' ) ) ) {
			$result['message']='Missing role';
			wp_send_json($result);
		}
		TMDS_DATA::villatheme_set_time_limit();
		$product_id = isset( $_POST['product_id'] ) ? absint( sanitize_text_field( wp_unslash( $_POST['product_id'] ) ) ) : '';
		$result     = array(
			'status'  => 'error',
			'message' => esc_html__( 'Not found', 'tmds-dropshipping-for-temu-and-woo' ),
		);
		if ( $product_id ) {
			if ( TMDS_Post::delete_post( $product_id, true ) ) {
				$result['status']  = 'success';
				$result['message'] = esc_html__( 'Removed', 'tmds-dropshipping-for-temu-and-woo' );
			} else {
				$result['message'] = esc_html__( 'Error in deleting the item', 'tmds-dropshipping-for-temu-and-woo' );
			}
		}
		wp_send_json( $result );
	}

	public static function save_attributes() {
		$result        = array(
			'status'       => 'error',
			'new_slug'     => '',
			'change_value' => false,
			'message'      => '',
		);
		$action = 'admin_ajax';
		if ( apply_filters( 'tmds_verify_ajax_nonce', true, $action) &&
		     !check_ajax_referer( 'tmds_' . $action, 'tmds_nonce', false )) {
			$result['message']='Invalid nonce';
            wp_send_json($result);
		}
		if ( ! current_user_can( apply_filters( 'villatheme_tmds_admin_sub_menu_capability', 'manage_woocommerce', 'tmds-import-list' ) ) ) {
			$result['message'] = 'Missing role';
			wp_send_json($result);
		}
		$prefix        = self::$prefix;
		$data          = isset( $_POST['form_data']["{$prefix}_product"] ) ? self::$settings::json_decode( wc_clean( wp_unslash( $_POST['form_data']["{$prefix}_product"] ) ) ) : [];// phpcs:ignore WordPress.Security.ValidatedSanitizedInput.InputNotSanitized
		$product_data  = array_values( $data )[0];
		$product_id    = array_keys( $data )[0];
		$new_attribute = $product_data['attributes'] ?? [];
		$attributes    = TMDS_Post::get_post_meta( $product_id, "_{$prefix}_attributes", true );
		$variations    = TMDS_Post::get_post_meta( $product_id, "_{$prefix}_variations", true );
		$change_slug   = '';
		$change_value  = false;
        $message_error = '';
		if ( ! empty( $new_attribute ) && is_array($new_attribute) && ! empty( $attributes ) ) {
            foreach ($new_attribute as $attribute_k  => $new_attribute_v){
                if (!isset($attributes[$attribute_k]) || empty($new_attribute_v['name'])){
                    $message_error = __('Attribute name can not be empty','tmds-dropshipping-for-temu-and-woo');
                    break;
                }
	            $new_slug       = self::$settings::sanitize_taxonomy_name( $new_attribute_v['name'] );
	            $attribute_slug = $attributes[ $attribute_k ]['slug_edited'] ?? $attributes[ $attribute_k ]['slug'] ?? '';
	            if ( ! self::is_attribute_value_equal( $new_slug, $attribute_slug )||
                     (isset($attributes[ $attribute_k ]['name_edited']) && $attributes[ $attribute_k ]['name_edited']!= $new_attribute_v['name']) ) {
		            $change_slug = $new_slug;
		            $attributes[ $attribute_k ]['slug_edited'] = $new_slug;
		            $attributes[ $attribute_k ]['name_edited'] = $new_attribute_v['name'];
		            $attributes[ $attribute_k ]['title_edited'] = $new_attribute_v['name'];
	            }
	            if ( ! empty( $new_attribute_v['values'] ) ) {
		            $new_values    = $new_attribute_v['values'];
		            $values_edited = $attributes[ $attribute_k ]['values_edited'] ?? $attributes[ $attribute_k ]['values'] ?? [];
		            foreach ( $values_edited as $value_k => $value ) {
                        $value_id = $value['id']??'';
                        $value_title = $value['title']??'';
                        if (isset($value_id) && isset($new_values[$value_id])){
	                        $new_value_title = trim( $new_values[ $value_id ] );
                            if ($value_title != $new_value_title){
	                            $change_value = true;
                                $values_edited[$value_k]['title'] = $new_value_title;
	                            foreach ( $variations as $variation_k => $variation ) {
		                            $v_attributes = $variation['attributes_edited'] ?? $variation['attributes'] ?? [];
                                    if (isset($v_attributes[$attribute_k]['id']) && $v_attributes[$attribute_k]['id'] == $value_id ) {
	                                    $v_attributes[ $attribute_k ]                    = $values_edited[ $value_k ];
	                                    $variations[ $variation_k ]['attributes_edited'] = $v_attributes;
                                    }
	                            }
	                            $attributes[ $attribute_k ]['values_edited'] = $values_edited;
                            }
                        }
		            }
	            }
            }
		}

        if ($message_error){
            $result['message'] = $message_error;
        }else {
	        if ( $change_slug || $change_value ) {
		        $result['status'] = 'success';
		        TMDS_Post::update_post_meta( $product_id, "_{$prefix}_attributes", $attributes );
		        TMDS_Post::update_post_meta( $product_id, "_{$prefix}_variations", $variations );
	        }
	        $result['new_slug']     = $change_slug;
	        $result['change_value'] = $change_value;
        }
		wp_send_json( $result );
	}

	public static function remove_attribute() {
		$result           = array(
			'status'  => 'error',
			'html'    => '',
			'message' => esc_html__( 'Invalid data', 'tmds-dropshipping-for-temu-and-woo' ),
		);
		$action = 'admin_ajax';
		if ( apply_filters( 'tmds_verify_ajax_nonce', true, $action) &&
		     !check_ajax_referer( 'tmds_' . $action, 'tmds_nonce', false )) {
			$result['message']='Invalid nonce';
			wp_send_json($result);
		}
		if ( ! current_user_can( apply_filters( 'villatheme_tmds_admin_sub_menu_capability', 'manage_woocommerce', 'tmds-import-list' ) ) ) {
			$result['message']='Missing role';
			wp_send_json($result);
		}
		$prefix           = self::$prefix;
		$data             = isset( $_POST['form_data']["{$prefix}_product"] ) ? TMDS_DATA::json_decode( wc_clean( wp_unslash( $_POST['form_data']["{$prefix}_product"] ) ) ) : [];// phpcs:ignore WordPress.Security.ValidatedSanitizedInput.InputNotSanitized
		$product_data     = array_values( $data )[0];
		$product_id       = array_keys( $data )[0];
		$remove_attribute = $product_data['attributes'] ?? [];
		$attribute_value  = isset( $_POST['attribute_value'] ) ? sanitize_text_field( wp_unslash( $_POST['attribute_value'] ) ) : '';
		$product          = TMDS_Post::get_post( $product_id );
		if ( $product && $product->post_type === "{$prefix}_draft_product" && in_array( $product->post_status, [ 'draft', 'override' ] ) ) {
			$attributes = TMDS_Post::get_post_meta( $product_id, "_{$prefix}_attributes", true );
			$variations = TMDS_Post::get_post_meta( $product_id, "_{$prefix}_variations", true );

			if ( self::remove_product_attribute( $product_id, $remove_attribute, $attribute_value, '', $attributes, $variations ) ) {
				$result['status'] = 'success';
				$result['message'] = esc_html__( 'Remove attribute successfully', 'tmds-dropshipping-for-temu-and-woo' );
			}
		} else {
			$result['message'] = esc_html__( 'Invalid product', 'tmds-dropshipping-for-temu-and-woo' );
		}
		wp_send_json( $result );
	}

	public static function remove_product_attribute( $product_id, $remove_attribute, $attribute_value, $split_variations, &$attributes, &$variations ) {
		$remove = false;
		$prefix = self::$prefix;
		if ( ! empty( $remove_attribute ) && ! empty( $attributes ) ) {
			$new_attribute_v = array_values( $remove_attribute )[0];
			$attribute_k     = array_keys( $remove_attribute )[0];
            if (isset($attributes[$attribute_k])){
	            foreach ( $variations as $variation_k => $variation ) {
		            $v_attributes = $variation['attributes_edited'] ?? $variation['attributes'] ?? [];
		            if (isset($v_attributes[$attribute_k]['id']) && $v_attributes[$attribute_k]['id'] == $attribute_value ) {
			            unset($v_attributes[ $attribute_k ]);
			            $variations[ $variation_k ]['attributes_edited'] = $v_attributes;
		            }else{
			            unset( $variations[ $variation_k ] );
		            }
	            }
	            unset( $attributes[ $attribute_k ] );
	            $variations = array_values( $variations );
	            TMDS_Post::update_post_meta( $product_id, "_{$prefix}_attributes", $attributes );
	            TMDS_Post::update_post_meta( $product_id, "_{$prefix}_variations", $variations );

	            if ( is_array( $split_variations ) ) {
		            TMDS_Post::update_post_meta( $product_id, "_{$prefix}_split_variations", $split_variations );
	            }
	            $remove = true;
            }
		}
		return $remove;
	}

	public static function is_attribute_value_equal( $value_1, $value_2 ) {
		return self::$settings::strtolower( $value_1 ) === self::$settings::strtolower( $value_2 );
	}

	public static function load_variations_table() {
		$result     = array(
			'status' => 'error',
			'message'   => esc_html__( 'Missing required arguments', 'tmds-dropshipping-for-temu-and-woo' )
		);
		$action = 'admin_ajax';
		if ( apply_filters( 'tmds_verify_ajax_nonce', true, $action) &&
		     !check_ajax_referer( 'tmds_' . $action, 'tmds_nonce', false )) {
			$result['message']='Invalid nonce';
			wp_send_json($result);
		}
		$key        = isset( $_GET['product_index'] ) ? absint( sanitize_text_field( wp_unslash( $_GET['product_index'] ) ) ) : '';
		$product_id = isset( $_GET['product_id'] ) ? absint( sanitize_text_field( wp_unslash( $_GET['product_id'] ) ) ) : '';
		if ( $key > - 1 && $product_id ) {
			$wc_currency            = get_woocommerce_currency();
			$wc_currency_symbol     = get_woocommerce_currency_symbol();
			$wc_decimals            = wc_get_price_decimals();
			$wc_decimals            = $wc_decimals < 1 ? 1 : pow( 10, ( - 1 * $wc_decimals ) );
			$use_different_currency = false;
			$import_info            = TMDS_Post::get_post_meta( $product_id, '_tmds_import_info', true );
			$currency               = $import_info['currency_code'] ?? $wc_currency;
			$decimals               = $import_info['currency_decimals'] ?? $import_info['temu_locale_settings']['currency']['number_precision'] ?? 2;
			$decimals               = $decimals < 1 ? 1 : pow( 10, ( - 1 * $decimals ) );
			if ( self::$settings::strtolower( $wc_currency ) != self::$settings::strtolower( $currency ) ) {
				$use_different_currency = true;
			}
			$parent     = [];
			$attributes = self::get_product_attributes( $product_id );
			if ( is_array( $attributes ) && ! empty( $attributes ) ) {
				foreach ( $attributes as $attribute_k => $attribute_v ) {
					$parent[ $attribute_k ] = $attribute_v['slug'] ?? '';
				}
			}
			$manage_stock = self::$settings->get_params( 'manage_stock' );
			$variations   = self::get_product_variations( $product_id );
			self::variation_html( $key, $parent, $attributes, $manage_stock, $variations, $use_different_currency, $currency, $decimals, $product_id, $wc_currency_symbol, $wc_decimals );
			$return                     = ob_get_clean();
			$result['status']           = 'success';
			$result['data']             = $return;
			$result['variations_count'] = self::$variations_count;
		}
		wp_send_json( $result );
	}

	public static function variation_html( $key, $parent, $attributes, $manage_stock, $variations, $use_different_currency, $currency, $decimals, $product_id, $wc_currency_symbol, $wc_decimals ) {
		?>
        <thead>
        <tr>
            <td width="1%"></td>
            <td class="tmds-fix-width">
                <input type="checkbox" checked class="<?php echo esc_attr( 'tmds-variations-bulk-enable tmds-variations-bulk-enable-' . $key ) ?>">
            </td>
            <td class="tmds-fix-width">
                <input type="checkbox" checked class="tmds-variations-bulk-select-image">
            </td>
            <th class="tmds-fix-width"><?php esc_html_e( 'Default variation', 'tmds-dropshipping-for-temu-and-woo' ) ?></th>
            <th><?php esc_html_e( 'Sku', 'tmds-dropshipping-for-temu-and-woo' ) ?></th>
			<?php
			if ( is_array( $parent ) && !empty( $parent ) ) {
				foreach ( $parent as $parent_k => $parent_v ) {
					if (empty($attributes[ $parent_k ]['set_variation'])){
						continue;
					}
					?>
                    <th class="tmds-attribute-filter-list-container">
						<?php
						$attribute_name = isset( $attributes[ $parent_k ]['name'] ) ? $attributes[ $parent_k ]['name'] : self::$settings::get_attribute_name_by_slug( $parent_v );
						echo esc_html( $attribute_name );
						$attribute_values = isset( $attributes[ $parent_k ]['values'] ) ? $attributes[ $parent_k ]['values'] : [];
						if ( !empty( $attribute_values ) ) {
							?>
                            <ul class="tmds-attribute-filter-list" data-attribute_slug="<?php echo esc_attr( $parent_v ) ?>">
								<?php
								foreach ( $attribute_values as $attribute_value ) {
									?>
                                    <li class="tmds-attribute-filter-item" title="<?php echo esc_attr( $attribute_value['title']??'' ) ?>"
                                        data-attribute_slug="<?php echo esc_attr( $parent_v ) ?>" data-attribute_value="<?php echo esc_attr( trim( $attribute_value['id']??$attribute_value['title']??'' ) ) ?>">
										<?php echo esc_html( $attribute_value['title']??'' ) ?>
                                    </li>
									<?php
								}
								?>
                            </ul>
							<?php
						}
						?>
                    </th>
					<?php
				}
			}
			?>
            <th>
				<?php esc_html_e( 'Cost', 'tmds-dropshipping-for-temu-and-woo' ) ?>
            </th>
            <th class="tmds-sale-price-col">
				<?php esc_html_e( 'Sale price', 'tmds-dropshipping-for-temu-and-woo' ) ?>
                <div class="tmds-set-price" data-set_price="sale_price">
					<?php esc_html_e( 'Set price', 'tmds-dropshipping-for-temu-and-woo' ) ?>
                </div>
            </th>
            <th class="tmds-regular-price-col">
				<?php esc_html_e( 'Regular price', 'tmds-dropshipping-for-temu-and-woo' ) ?>
                <div class="tmds-set-price ?>" data-set_price="regular_price">
					<?php esc_html_e( 'Set price', 'tmds-dropshipping-for-temu-and-woo' ) ?>
                </div>
            </th>
			<?php
			if ( $manage_stock ) {
				?>
                <th class="tmds-inventory-col"><?php esc_html_e( 'Inventory', 'tmds-dropshipping-for-temu-and-woo' ) ?></th>
				<?php
			}
			?>
        </tr>
        </thead>
        <tbody>
		<?php
		foreach ( $variations as $variation_key => $variation ) {
			$variation_image = $variation['image'] ?? '';
			$inventory               = min(floatval( $variation['stock']), floatval( $variation['limit_qty'] ));
			$variation_sale_price    = $variation['sale_price'] ? self::$settings::string_to_float( $variation['sale_price'] ) : '';
			$variation_regular_price = self::$settings::string_to_float( $variation['regular_price'] );
			if (!empty($variation['is_on_sale']) && $variation_sale_price){
				$import_price = $variation_sale_price;
				$import_price_html = $variation['sale_price_html'] ??'';
			}else{
				$import_price            = $variation_regular_price;
				$import_price_html = $variation['regular_price_html'] ??'';
			}
			$price                   = self::$settings->process_exchange_price( $import_price, $currency );
			$cost_html               = wc_price( $price );
			if ( $use_different_currency ) {
				$cost_html = $import_price_html? "{$import_price_html}({$cost_html})" :wc_price( $import_price, [
						'currency'     => $currency,
						'decimals'     => $decimals,
						'price_format' => '%1$s&nbsp;%2$s'
					] ) . '(' . $cost_html . ')';
			}
			$sale_price            = self::$settings->process_price( $price, true );
			$regular_price         = self::$settings->process_price( $price );
			$image_src             = $variation_image ? $variation_image : wc_placeholder_img_src();
			$checked               = '';
			$variation_image_class = array( 'tmds-variation-image' );
			if ( empty( $split_variations ) ) {
				$checked                 = 'checked';
				$variation_image_class[] = 'tmds-selected-item';
				self::$variations_count ++;
			}
			?>
            <tr class="tmds-product-variation-row">
                <td class="tmds-product-variation-row-number"><?php echo esc_html( $variation_key + 1 ) ?></td>
                <td>
                    <input type="checkbox" <?php echo esc_attr( $checked ) ?>
                           class="<?php echo esc_attr( implode( ' ', array(
						       'tmds-variation-enable',
						       'tmds-variation-enable-' . $key,
						       'tmds-variation-enable-' . $key . '-' . $variation_key
					       ) ) ) ?>">
                </td>
                <td>
                    <div class="<?php echo esc_attr( implode( ' ', $variation_image_class ) ) ?>">
                        <span class="tmds-selected-item-icon-check"> </span>
                        <?php // The displayed images are not yet saved to the WP media library — they are only shown for user selection. ?>
                        <img data-image_src="<?php echo esc_url( $image_src ) // phpcs:ignore PluginCheck.CodeAnalysis.ImageFunctions.NonEnqueuedImage ?>"
                             src="<?php echo esc_url( $image_src ) ?>"
                             class="tmds-import-data-variation-image">
                        <input type="hidden" value="<?php echo esc_attr( $variation_image ? $variation_image : '' ) ?>"
                               name="<?php echo esc_attr( 'tmds_product[' . $product_id . '][variations][' . $variation_key . '][image]' ) ?>">
                    </div>
                </td>
                <td><input type="radio" value="<?php echo esc_attr( $variation['skuId'] ) ?>"
                           class="tmds-import-data-variation-default"
                           name="<?php echo esc_attr( 'tmds_product[' . $product_id . '][default_variation]' ) ?>">
                </td>
                <td>
                    <div>
                        <input type="text" value="<?php echo esc_attr( $variation['skuId'] ) ?>"
                               name="<?php echo esc_attr( 'tmds_product[' . $product_id . '][variations][' . $variation_key . '][sku]' ) ?>"
                               class="tmds-import-data-variation-sku">
                        <input type="hidden" value="<?php echo esc_attr( $variation['skuId'] ) ?>"
                               class="tmds-import-data-variation-skuId"
                               name="<?php echo esc_attr( 'tmds_product[' . $product_id . '][variations][' . $variation_key . '][skuId]' ) ?>">

                    </div>
                </td>
				<?php
				if ( is_array( $parent ) && !empty( $parent ) ) {
					foreach ( $parent as $parent_k => $parent_v ) {
						if (empty($attributes[ $parent_k ]['set_variation'])){
							continue;
						}
						$tmp_title = isset( $variation['attributes'][ $parent_k ]['title'] ) ? $variation['attributes'][ $parent_k ]['title'] : '';
						$tmp_id = isset( $variation['attributes'][ $parent_k ]['id'] ) ? $variation['attributes'][ $parent_k ]['id'] : '';
						?>
                        <td>
                            <input type="hidden" data-attribute_slug="<?php echo esc_attr( $parent_v ) ?>"
                                   data-attribute_value="<?php echo esc_attr( $tmp_id ) ?>"
                                   name="<?php echo esc_attr( 'tmds_product[' . $product_id . '][variations][' . $variation_key . '][attributes][' . $parent_k . '][' . $tmp_id . ']' ) ?>"
                                   class="tmds-import-data-variation-attribute"
                                   value="<?php echo esc_attr( $tmp_title ) ?>">
							<?php echo wp_kses_post( $tmp_title ); ?>
                        </td>
						<?php
					}
				}
				?>
                <td>
                    <div class="tmds-price-field">
                        <span class="tmds-import-data-variation-cost">
                            <?php echo wp_kses( $cost_html,TMDS_DATA::filter_allowed_html() )  ?>
                        </span>
                    </div>
                </td>
                <td>
                    <div class="vi-ui left labeled input">
                        <label for="amount"
                               class="vi-ui label"><?php echo esc_html( $wc_currency_symbol ) ?></label>
                        <input type="number" min="0" step="<?php echo esc_attr( $wc_decimals ) ?>"
                               value="<?php echo esc_attr( is_numeric($sale_price)? $sale_price :'' ) ?>" data-allow_empty="1"
                               name="<?php echo esc_attr( 'tmds_product[' . $product_id . '][variations][' . $variation_key . '][sale_price]' ) ?>"
                               class="tmds-import-data-variation-sale-price">
                    </div>
                </td>
                <td>
                    <div class="vi-ui left labeled input">
                        <label for="amount"
                               class="vi-ui label"><?php echo esc_html( $wc_currency_symbol ) ?></label>
                        <input type="number" min="0" step="<?php echo esc_attr( $wc_decimals ) ?>"
                               value="<?php echo esc_attr( $regular_price ) ?>"
                               name="<?php echo esc_attr( 'tmds_product[' . $product_id . '][variations][' . $variation_key . '][regular_price]' ) ?>"
                               class="tmds-import-data-variation-regular-price">
                    </div>
                </td>
				<?php
				if ( $manage_stock ) {
					?>
                    <td>
                        <input type="number" min="0" step="<?php echo esc_attr( $wc_decimals ) ?>"
                               value="<?php echo esc_attr( $inventory ) ?>"
                               name="<?php echo esc_attr( 'tmds_product[' . $product_id . '][variations][' . $variation_key . '][stock]' ) ?>"
                               class="tmds-import-data-variation-inventory">
                    </td>
					<?php
				}
				?>
            </tr>
			<?php
		}
		?>
        </tbody>
		<?php
	}

	public static function page_callback() {
		$user     = wp_get_current_user();
		$per_page = (int) $user->get( self::$prefix . '_import_list_per_page' );
		if ( empty ( $per_page ) || $per_page < 1 ) {
			$per_page = 20;
		}
		$paged     = isset( $_GET['paged'] ) ? (int) sanitize_text_field( wp_unslash( $_GET['paged'] ) ) : 1;// phpcs:ignore WordPress.Security.NonceVerification.Recommended
		$args      = array(
			'post_type'      => self::$prefix . '_draft_product',
			'post_status'    => array( 'draft', 'override' ),
			'order'          => 'DESC',
			'orderby'        => 'date',
			'fields'         => 'ids',
			'posts_per_page' => $per_page,
			'paged'          => $paged,
		);
		$search_id = isset( $_GET[ self::$prefix . '_search_id' ] ) ? sanitize_text_field( wp_unslash( $_GET[ self::$prefix . '_search_id' ] ) ) : '';// phpcs:ignore WordPress.Security.NonceVerification.Recommended
		$keyword   = isset( $_GET[ self::$prefix . '_search' ] ) ? sanitize_text_field( wp_unslash( $_GET[ self::$prefix . '_search' ] ) ) : '';// phpcs:ignore WordPress.Security.NonceVerification.Recommended
		if ( $search_id ) {
			$args['post__in']       = array( $search_id );
			$args['posts_per_page'] = 1;
			$keyword                = '';
		} else if ( $keyword ) {
			$args['s'] = $keyword;
		}
		$the_query   = TMDS_Post::query( $args );
		$product_ids = $the_query->get_posts();
		$count       = $the_query->found_posts;
		$total_page  = $the_query->max_num_pages;
		wp_reset_postdata();
		?>
        <div class="wrap tmds-import-list-wrap">
            <h2 class="tmds-import-list-head">
				<?php esc_html_e( 'Import List', 'tmds-dropshipping-for-temu-and-woo' ) ?>
                <div class="tmds-import-list-head-action">
					<?php
					if ( ! $search_id && ! $keyword && $paged === 1 ) {
						self::$settings::chrome_extension_buttons();
					}
					?>
                </div>
            </h2>
			<?php
			if ( is_array( $product_ids ) && ! empty( $product_ids ) ) {
				/*After a product is imported, its html content(Import list) will be removed*/
				/*The first wp_editor call includes css file for all editors so call it here and hide it so that editor css is not removed after the first product is imported*/
				wc_get_template( 'admin/html-import-list-header-section.php',
					array(
						'count'      => $count,
						'total_page' => $total_page,
						'paged'      => $paged,
						'per_page'   => $per_page,
						'keyword'    => $keyword,
					),
					'',
					TMDS_TEMPLATES );
				$wc_decimals            = wc_get_price_decimals();
				$wc_decimals            = $wc_decimals < 1 ? 1 : pow( 10, ( - 1 * $wc_decimals ) );
				$key                    = 0;
				$currency               = 'USD';
				$wc_currency            = get_woocommerce_currency();
				$wc_currency_symbol     = get_woocommerce_currency_symbol( $wc_currency );
				$use_different_currency = false;
				extract( [
					'default_select_image'       => self::$settings->get_params( 'product_gallery' ),
					'product_status'             => self::$settings->get_params( 'product_status' ),
					'product_shipping_class'     => self::$settings->get_params( 'product_shipping_class' ),
					'catalog_visibility'         => self::$settings->get_params( 'catalog_visibility' ),
					'manage_stock'               => self::$settings->get_params( 'manage_stock' ),
					'product_tags'               => self::$settings->get_params( 'product_tags' ),
					'product_categories'         => self::$settings->get_params( 'product_categories' ),
					'category_options'           => self::$settings::get_product_categories(),
					'tags_options'               => self::$settings::get_product_tags(),
					'shipping_class_options'     => self::$settings::get_shipping_class_options(),
					'catalog_visibility_options' => self::$settings::get_catalog_visibility_options(),
					'product_status_options'     => self::$settings::get_product_status_options(),
				] );
				do_action( 'tmds_import_list_before_products_list' );
				printf( '<div class="vi-ui segment tmds-import-list">' );
				foreach ( $product_ids as $product_id ) {
					$parent     = [];
					$product    = TMDS_Post::get_post( $product_id );
					$attributes = self::get_product_attributes( $product_id );
					if ( is_array( $attributes ) && count( $attributes ) ) {
						foreach ( $attributes as $attribute_k => $attribute_v ) {
							$parent[ $attribute_k ] = $attribute_v['slug'] ?? '';
						}
					}
					$import_info     = TMDS_Post::get_post_meta( $product_id, '_'. self::$settings::$prefix .'_import_info', true );
					$currency        = $import_info['currency_code'] ?? $currency;
					$currency_symbol = $import_info['currency_symbol'] ?? '';
					$decimals        = $import_info['currency_decimals'] ?? 2;
					$decimals        = $decimals < 1 ? 1 : pow( 10, ( - 1 * $decimals ) );
					if (self::$settings::strtolower( $wc_currency ) != self::$settings::strtolower( $currency ) ) {
						$use_different_currency = true;
					}
					$custom_pd_options = [];
					$gallery = TMDS_Post::get_post_meta( $product_id, '_tmds_gallery', true );
					if ( ! $gallery || ! is_array( $gallery ) ) {
						$gallery = [];
					}
					$desc_images      = TMDS_Post::get_post_meta( $product_id, '_tmds_description_images', true );
					$variation_images = TMDS_Post::get_post_meta( $product_id, '_tmds_variation_images', true );
					$price_alert      = false;
					$accordion_class  = [
						'vi-ui',
						'styled',
						'fluid',
						'accordion',
						'active',
						'tmds-accordion',
						'tmds-product-row'
					];
					if ( $price_alert ) {
						$accordion_class['personalization'] = 'tmds-product-price-alert';
					}
					$arg = array(
						'wc_decimals'                => $wc_decimals,
						'wc_currency_symbol'         => $wc_currency_symbol,
						'currency'                   => $currency,
						'decimals'                   => $decimals,
						'use_different_currency'     => $use_different_currency,
						'product_shipping_class'     => $product_shipping_class,
						'shipping_class_options'     => $shipping_class_options,
						'catalog_visibility_options' => $catalog_visibility_options,
						'catalog_visibility'         => $catalog_visibility,
						'category_options'           => $category_options,
						'tags_options'               => $tags_options,
						'product_status_options'     => $product_status_options,
						'product_status'             => $product_status,
						'product_tags'               => $product_tags,
						'product_categories'         => $product_categories,
						'accordion_class'            => $accordion_class,
						'product_id'                 => $product_id,
						'product'                    => $product,
						'price_alert'                => $price_alert,
						'key'                        => $key,
						'sku'                        => TMDS_Post::get_post_meta( $product_id, '_tmds_sku', true ),
						'store_info'                 => TMDS_Post::get_post_meta( $product_id, '_tmds_store_info', true ),
						'import_info'                => [
							$import_info['region_name'] ?? $import_info['region_code'] ?? $import_info['temu_locale_settings']['region']['name'] ?? '',
							$import_info['language_name'] ?? $import_info['temu_locale_settings']['language']['name'] ?? '',
							$currency_symbol ? $currency_symbol . '(' . $currency . ')' : $currency
						],
						'gallery'                    => $gallery,
						'default_select_image'       => $default_select_image,
						'image'                      => isset( $gallery[0] ) ? $gallery[0] : '',
						'variations'                 => self::get_product_variations( $product_id ),
						'variation_images'           => ! $variation_images ? [] : array_values( array_unique( $variation_images ) ),
						'desc_images'                => ! $desc_images ? [] : array_values( array_unique( $desc_images ) ),
						'is_variable'                => is_array( $parent ) && ! empty( $parent ),
						'manage_stock'               => $manage_stock,
						'attributes'                 => $attributes,
						'custom_pd_options'          => $custom_pd_options,
					);
					if ( empty( $arg['is_variable'] ) && ! empty( $arg['variations'][0]['sku'] ) ) {
						$product_sku_tmp = $arg['variations'][0]['sku'];
					} else {
						$product_sku_tmp = $arg['sku'] ?? '';
					}
					$arg['product_sku'] = $product_sku_tmp;
					wc_get_template( 'admin/html-import-list-item.php', $arg, '', TMDS_TEMPLATES );
					$key ++;
				}
				printf( '</div>' );
				wc_get_template( 'admin/html-import-list-bulk-action-modal.php',
					array(
						'category_options'       => $category_options,
						'tags_options'           => $tags_options,
						'shipping_class_options' => $shipping_class_options,
					),
					'',
					TMDS_TEMPLATES );
			} else {
				?>
                <div class="vi-ui small segment">
                    <p><?php esc_html_e( 'No products found', 'tmds-dropshipping-for-temu-and-woo' ); ?></p>
                </div>
				<?php
			}
			?>
        </div>
		<?php
	}

	public static function simple_product_price_field_html( $key, $manage_stock, $variations, $use_different_currency, $currency, $decimals, $product_id, $wc_currency_symbol, $wc_decimals ) {
		if ( empty( $variations ) ) {
			return;
		}
		$variation = $variations[0];
		$inventory               = min(floatval( $variation['stock'] ), floatval( $variation['limit_qty'] ));
		$variation_sale_price    = self::$settings::string_to_float( $variation['sale_price'] ?? 0 );
		$variation_regular_price = self::$settings::string_to_float( $variation['regular_price'] );
        if (!empty($variation['is_on_sale']) && $variation_sale_price){
            $import_price = $variation_sale_price;
            $import_price_html = $variation['sale_price_html']??'';
        }else{
	        $import_price            = $variation_regular_price;
	        $import_price_html = $variation['regular_price_html']??'';
        }
		$price                   = self::$settings->process_exchange_price( $import_price, $currency );
		$cost_html               = $import_price_html;
		if ( $use_different_currency ) {
			$cost_html               = wc_price( $price );
			$cost_html = $import_price_html ?  "{$import_price_html}({$cost_html})" :  wc_price( $import_price, [
					'currency'     => $currency,
					'decimals'     => $decimals,
					'price_format' => '%1$s&nbsp;%2$s'
				] ) . '(' . $cost_html . ')';
		}
		$sale_price    = self::$settings->process_price( $price, true );
		$regular_price = self::$settings->process_price( $price );
		?>
        <div class="field tmds-simple-product-price-field">
            <input type="hidden"
                   name="<?php echo esc_attr( 'tmds_product[' . $product_id . '][variations][0][skuId]' ) ?>"
                   value="<?php echo esc_attr( $variation['skuId'] ?? '' ) ?>">
            <div class="equal width fields">
                <div class="field">
                    <label><?php esc_html_e( 'Cost', 'tmds-dropshipping-for-temu-and-woo' ); ?></label>
                    <div class="tmds-price-field">
						<?php echo wp_kses( $cost_html ,TMDS_DATA::filter_allowed_html()) ?>
                    </div>
                </div>
                <div class="field">
                    <label><?php esc_html_e( 'Sale price', 'tmds-dropshipping-for-temu-and-woo' ) ?></label>
                    <div class="vi-ui left labeled input">
                        <label for="amount"
                               class="vi-ui label"><?php echo esc_html( $wc_currency_symbol ) ?></label>
                        <input type="number" min="0" step="<?php echo esc_attr( $wc_decimals ) ?>"
                               value="<?php echo esc_attr( is_numeric($sale_price) ? $sale_price : '' ) ?>"
                               name="<?php echo esc_attr( 'tmds_product[' . $product_id . '][variations][0][sale_price]' ) ?>"
                               class="tmds-import-data-variation-sale-price">
                    </div>
                </div>
                <div class="field">
                    <label><?php esc_html_e( 'Regular price', 'tmds-dropshipping-for-temu-and-woo' ) ?></label>
                    <div class="vi-ui left labeled input">
                        <label for="amount"
                               class="vi-ui label"><?php echo esc_html( $wc_currency_symbol ) ?></label>
                        <input type="number" min="0" step="<?php echo esc_attr( $wc_decimals ) ?>"
                               value="<?php echo esc_attr( $regular_price ) ?>"
                               name="<?php echo esc_attr( 'tmds_product[' . $product_id . '][variations][0][regular_price]' ) ?>"
                               class="tmds-import-data-variation-regular-price">
                    </div>
                </div>
				<?php
				if ( $manage_stock ) {
					?>
                    <div class="field">
                        <label><?php esc_html_e( 'Inventory', 'tmds-dropshipping-for-temu-and-woo' ) ?></label>
                        <input type="number" min="0" step="<?php echo esc_attr( $wc_decimals ) ?>"
                               value="<?php echo esc_attr( $inventory ) ?>"
                               name="<?php echo esc_attr( 'tmds_product[' . $product_id . '][variations][0][stock]' ) ?>"
                               class="tmds-import-data-variation-inventory">
                    </div>
					<?php
				}
				?>
            </div>
        </div>
		<?php
	}

	public static function get_product_variations( $product_id ) {
		$variations = TMDS_Post::get_post_meta( $product_id, '_tmds_variations', true );
		if ( is_array( $variations ) && count( $variations ) ) {
			foreach ( $variations as $key => $value ) {
				if ( ! empty( $value['attributes_edited'] ) ) {
					$variations[ $key ]['attributes'] = $value['attributes_edited'];
					unset( $variations[ $key ]['attributes_edited'] );
				}
			}
		}

		return $variations;
	}

	public static function get_product_attributes( $product_id ) {
		$attributes = TMDS_Post::get_post_meta( $product_id, '_tmds_attributes', true );
		if ( is_array( $attributes ) && count( $attributes ) ) {
			foreach ( $attributes as $key => $value ) {
				if ( ! empty( $value['slug_edited'] ) ) {
					$attributes[ $key ]['slug'] = $value['slug_edited'];
					unset( $attributes[ $key ]['slug_edited'] );
				}
				if ( ! empty( $value['name_edited'] ) ) {
					$attributes[ $key ]['name'] = $value['name_edited'];
					unset( $attributes[ $key ]['name_edited'] );
				}
				if ( ! empty( $value['title_edited'] ) ) {
					$attributes[ $key ]['title'] = $value['title_edited'];
					unset( $attributes[ $key ]['title_edited'] );
				}
				if ( ! empty( $value['values_edited'] ) ) {
					$attributes[ $key ]['values'] = $value['values_edited'];
					unset( $attributes[ $key ]['values_edited'] );
				}
			}
		}

		return $attributes;
	}

	public static function screen_options_page() {
		add_screen_option( 'per_page', array(
			'label'   => esc_html__( 'Number of items per page', 'tmds-dropshipping-for-temu-and-woo' ),
			'default' => 5,
			'option'  => self::$prefix . '_import_list_per_page'
		) );
	}

	public function marketplace_suggestions_products_empty_state() {
		self::$settings::enqueue_script(
			array( self::$prefix . '-admin-settings' ),
			array( 'admin-settings' ),
		);
		wp_localize_script( self::$prefix . '-admin-settings', 'tmds_params', array(
			'go_to_tmds_bt' => sprintf( '<a target="_blank" href="https://www.temu.com/" class="woocommerce-BlankState-cta button tmds-import-products">%s</a>', esc_html__( 'Go to Temu Products', 'tmds-dropshipping-for-temu-and-woo' ) )
		) );
	}

	/**
	 * Add notices when images are being imported, when images are in the queue but not processed, when images are all imported or when cron is late
	 */
	public function admin_notices() {
		if ( self::$process_image->is_process_running() ) {
			$is_late = false;
			$next    = wp_next_scheduled( 'wp_' . self::$prefix . '_background_download_images_cron' );
			if ( $next ) {
				$late = $next - time();
				if ( $late < - 300 ) {
					$is_late = true;
				}
			}
			if ( $is_late ) {
				?>
                <div class="notice notice-error">
                    <p>
						<?php
						/* translators: 1: plugin name. 2: href */
						printf( wp_kses_post( __( '<strong>%1$s</strong>: <i>wp_%2$s_background_download_images_cron</i> is late, queued product images may not be processed. If you want to move all queued images to Failed images page to handle them manually, please click <a href="%3$s">Move</a>',
							'tmds-dropshipping-for-temu-and-woo' )), esc_attr(TMDS_NAME),esc_html(self::$prefix ), esc_url(wp_nonce_url( add_query_arg( array( self::$prefix . '_move_queued_images' => 1 ) ) )) );
						?>
                    </p>
                </div>
				<?php
			} else {
				?>
                <div class="notice notice-warning">
                    <p>
						<?php
						/* translators: %s: plugin name */
						printf( wp_kses_post( __( '<strong>%s</strong>: Product images are still being processed in the background, please do not edit products/go to product edit page until all images are processed completely.',
							'tmds-dropshipping-for-temu-and-woo' ) ), wp_kses_post(TMDS_NAME) );
						?>
                    </p>
                </div>
				<?php
			}
		} else {
			if ( self::$process_image->is_queue_empty() ) {
				if ( get_transient( 'villatheme_'.self::$prefix . '_background_download_images_complete' ) ) {
					delete_transient( 'villatheme_'.self::$prefix . '_background_download_images_complete' );
					?>
                    <div class="updated">
                        <p>
							<?php
							/* translators: %s: plugin name*/
							printf( wp_kses_post( __( '<strong>%s</strong>: Finish processing product images', 'tmds-dropshipping-for-temu-and-woo' ) ), wp_kses_post(TMDS_NAME ));
							?>
                        </p>
                    </div>
					<?php
				}
			} else {
				?>
                <div class="notice notice-warning">
                    <p>
						<?php
						/* translators: 1: plugin name. 2: href */
						printf( wp_kses_post( __( '<strong>%1$s</strong>: There are still images in the queue but background process is not running. <a href="%2$s">Run</a> or <a href="%3$s">Move to Failed images</a>',
							'tmds-dropshipping-for-temu-and-woo' )), wp_kses_post(TMDS_NAME), esc_url(wp_nonce_url( add_query_arg( array( self::$prefix . '_run_download_product_image' => 1 ) )) ),
							esc_url(wp_nonce_url( add_query_arg( array( self::$prefix . '_move_queued_images' => 1 ) ) )) ) ?>
                    </p>
                </div>
				<?php
			}
		}
	}

	public function menu_product_count() {
		global $submenu;
		if ( isset( $submenu[ self::$prefix ] ) ) {
			// Add count if user has access.
			if ( apply_filters( 'villatheme_'.self::$prefix . '_product_count_in_menu', true )
			     || current_user_can( apply_filters( 'villatheme_'.self::$prefix . '_admin_sub_menu_capability', 'manage_woocommerce', self::$prefix . '-import-list' ) )
			) {
				$count         = TMDS_Post::count_posts( 'tmds_draft_product', 'readable' );
				$product_count = floatval( $count->draft ?? 0 ) + floatval( $count->override ?? 0 );
				foreach ( $submenu[ self::$prefix ] as $key => $menu_item ) {
					if ( ! empty( $menu_item[2] ) && $menu_item[2] === self::$prefix ) {
						$count_label                         = sprintf( " <span class='update-plugins count-%s'><span class='%s-import-list-count'>%s</span></span>",
							esc_attr( $product_count ), esc_attr( self::$prefix ), esc_html( number_format_i18n( $product_count ) ) );
						$submenu[ self::$prefix ][ $key ][0] .= $count_label;
					}
				}
			}
		}
	}

	public function empty_import_list() {
		$page = isset( $_GET['page'] ) ? sanitize_text_field( wp_unslash( $_GET['page'] ) ) : '';
		if ( ! empty( $_GET[ self::$prefix . '_empty_product_list' ] ) && $page === self::$prefix ) {
			if ( isset( $_GET['_wpnonce'] ) && wp_verify_nonce( sanitize_key( $_GET['_wpnonce'] ) ) ) {
				TMDS_Post::empty_import_list();
				wp_safe_redirect( admin_url( "admin.php?page={$page}" ) );
				exit();
			}
		}
	}

	/**
	 * Move all images that are process in the background to Failed images so that they can be imported manually
	 */
	public function move_queued_images() {
		global $wpdb;
		$prefix = self::$prefix;
		if ( ! empty( $_GET[ $prefix . '_move_queued_images' ] ) ) {
			$nonce = isset( $_GET['_wpnonce'] ) ? sanitize_text_field( wp_unslash( $_GET['_wpnonce'] ) ) : '';
			if ( wp_verify_nonce( $nonce ) ) {
				$results = $wpdb->get_results(// phpcs:ignore WordPress.DB.DirectDatabaseQuery.DirectQuery, WordPress.DB.DirectDatabaseQuery.NoCaching
                        $wpdb->prepare('select * from %i where option_name like %s',["{$wpdb->prefix}options","%{$prefix}_background_download_images_batch%" ])
                        , ARRAY_A );
				foreach ( $results as $result ) {
					$images = maybe_unserialize( $result['option_value'] );
					$delete = false;
					foreach ( $images as $image ) {
						if ( get_post_type( $image['woo_product_id'] ) === 'product' ) {
							if ( TMDS_Error_Images_Table::insert( $image['woo_product_id'], implode( ',', $image['product_ids'] ), $image['src'],
								intval( $image['set_gallery'] ) )
							) {
								$delete = true;
							}
						} else {
							$delete = true;
						}
					}
					if ( $delete ) {
						delete_option( $result['option_name'] );
					}
				}
				wp_safe_redirect( remove_query_arg( array( $prefix . '_move_queued_images', '_wpnonce' ) ) );
				exit();
			}
		}
	}

	public function background_process() {

		self::$process_image = TMDS_Download_Images::instance();

		$nonce = isset( $_REQUEST['_wpnonce'] ) ? sanitize_key( wp_unslash( $_REQUEST['_wpnonce'] ) ) : '';

		if ( wp_verify_nonce( $nonce ) ) {

			if ( ! empty( $_REQUEST[ self::$prefix . '_cancel_download_product_image' ] ) ) {
				self::$process_image->kill_process();
				wp_safe_redirect( @remove_query_arg( array( self::$prefix . '_cancel_download_product_image', '_wpnonce' ) ) );
				exit;
			}

			if ( ! empty( $_REQUEST[ self::$prefix . '_run_download_product_image' ] ) ) {
				if ( ! self::$process_image->is_process_running() && ! self::$process_image->is_queue_empty() ) {
					self::$process_image->dispatch();
				}
				wp_safe_redirect( @remove_query_arg( array( self::$prefix . '_run_download_product_image', '_wpnonce' ) ) );
				exit;
			}
		}
	}
}