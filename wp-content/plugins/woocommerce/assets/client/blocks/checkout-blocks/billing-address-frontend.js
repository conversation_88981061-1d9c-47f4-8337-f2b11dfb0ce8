(self.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=self.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[4037],{3127:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(1609),n=s(7723),i=s(3993),d=s(5703),a=s(6398),o=s(9155);s(1094);const l=({address:e,onEdit:t,target:s,fieldConfig:l,isExpanded:c})=>{const m=(0,d.getSetting)("countryData",{});let p=(0,d.getSetting)("defaultAddressFormat","{name}\n{company}\n{address_1}\n{address_2}\n{city}\n{state}\n{postcode}\n{country}");(0,i.objectHasProp)(m,null==e?void 0:e.country)&&(0,i.objectHasProp)(m[e.country],"format")&&(0,i.isString)(m[e.country].format)&&(p=m[e.country].format);const{name:u,address:h}=(0,a.M0)(e,p),g="shipping"===s?(0,n.__)("Edit shipping address","woocommerce"):(0,n.__)("Edit billing address","woocommerce");return(0,r.createElement)("div",{className:"wc-block-components-address-card"},(0,r.createElement)("address",null,(0,r.createElement)("span",{className:"wc-block-components-address-card__address-section"},u),(0,r.createElement)("div",{className:"wc-block-components-address-card__address-section"},h.filter((e=>!!e)).map(((e,t)=>(0,r.createElement)("span",{key:"address-"+t},e)))),e.phone&&!l.phone.hidden?(0,r.createElement)("div",{key:"address-phone",className:"wc-block-components-address-card__address-section"},e.phone):""),t&&(0,r.createElement)(o.$,{render:(0,r.createElement)("span",null),className:"wc-block-components-address-card__edit","aria-controls":s,"aria-expanded":c,"aria-label":g,onClick:e=>{e.preventDefault(),t()},type:"button"},(0,n.__)("Edit","woocommerce")))}},8202:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(1609),n=s(851);s(7259);const i=({isEditing:e=!1,addressCard:t,addressForm:s})=>{const i=(0,n.A)("wc-block-components-address-address-wrapper",{"is-editing":e});return(0,r.createElement)("div",{className:i},(0,r.createElement)("div",{className:"wc-block-components-address-card-wrapper"},t()),(0,r.createElement)("div",{className:"wc-block-components-address-form-wrapper"},s()))}},9017:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(7723);const n=({defaultTitle:e=(0,r.__)("Step","woocommerce"),defaultDescription:t=(0,r.__)("Step description text.","woocommerce"),defaultShowStepNumber:s=!0})=>({title:{type:"string",default:e},description:{type:"string",default:t},showStepNumber:{type:"boolean",default:s}})},9463:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>D});var r=s(1609),n=s(851),i=s(1616),d=s(4656),a=s(5251),o=s(7143),l=s(7594),c=s(6087),m=s(4375),p=s(2663),u=s(2379),h=s(3494),g=s(3505),E=s(3603),b=s(812),w=s(8202),_=s(3127);const f=({addressFieldsConfig:e})=>{const{billingAddress:t,setShippingAddress:s,setBillingAddress:n,useBillingAsShipping:i,editingBillingAddress:d,setEditingBillingAddress:m}=(0,a.C)(),{dispatchCheckoutEvent:p}=(0,E.y)(),{hasValidationErrors:u,invalidProps:h}=(0,o.useSelect)((e=>{const s=e(l.VALIDATION_STORE_KEY);return{hasValidationErrors:s.hasValidationErrors(),invalidProps:Object.keys(t).filter((e=>"email"!==e&&void 0!==s.getValidationError("billing_"+e))).filter(Boolean)}}));(0,c.useEffect)((()=>{h.length>0&&!1===d&&m(!0)}),[d,u,h.length,m]);const f=(0,c.useCallback)((e=>{n(e),i&&(s(e),p("set-shipping-address")),p("set-billing-address")}),[p,n,s,i]),A=(0,c.useCallback)((()=>(0,r.createElement)(_.A,{address:t,target:"billing",onEdit:()=>{m(!0)},fieldConfig:e,isExpanded:d})),[t,e,d,m]),y=(0,c.useCallback)((()=>(0,r.createElement)(r.Fragment,null,(0,r.createElement)(g.l,{id:"billing",addressType:"billing",onChange:f,values:t,fields:b.Hw,fieldConfig:e,isEditing:d}))),[e,t,f,d]);return(0,r.createElement)(w.A,{isEditing:d,addressCard:A,addressForm:y})},A=({showCompanyField:e=!1,requireCompanyField:t=!1,showApartmentField:s=!1,requireApartmentField:n=!1,showPhoneField:i=!1,requirePhoneField:g=!1})=>{const{billingAddress:E,setShippingAddress:b,useBillingAsShipping:w}=(0,a.C)(),{isEditor:_}=(0,p.m)();(0,m.Su)((()=>{if(w){const{email:t,...s}=E,r={...s};i||delete r.phone,e&&delete r.company,b(r)}}));const A=(0,c.useMemo)((()=>({company:{hidden:!e,required:t},address_2:{hidden:!s,required:n},phone:{hidden:!i,required:g}})),[e,t,s,n,i,g]),y=_?h.A:c.Fragment,C=w?[u.tG.BILLING_ADDRESS,u.tG.SHIPPING_ADDRESS]:[u.tG.BILLING_ADDRESS],{cartDataLoaded:F}=(0,o.useSelect)((e=>({cartDataLoaded:e(l.CART_STORE_KEY).hasFinishedResolution("getCartData")})));return(0,r.createElement)(c.Fragment,null,(0,r.createElement)(d.StoreNoticesContainer,{context:C}),(0,r.createElement)(y,null,F?(0,r.createElement)(f,{addressFieldsConfig:A}):null))};var y=s(9017),C=s(7723);const F=(0,C.__)("Billing address","woocommerce"),S=(0,C.__)("Enter the billing address that matches your payment method.","woocommerce"),k=(0,C.__)("Billing and shipping address","woocommerce"),v=(0,C.__)("Enter the billing and shipping address that matches your payment method.","woocommerce"),N={...(0,y.A)({defaultTitle:F,defaultDescription:S}),className:{type:"string",default:""},lock:{type:"object",default:{move:!0,remove:!0}}};var B=s(9292);const D=(0,i.withFilteredAttributes)(N)((({title:e,description:t,children:s,className:i})=>{const{showFormStepNumbers:c}=(0,B.Oy)(),m=(0,o.useSelect)((e=>e(l.CHECKOUT_STORE_KEY).isProcessing())),{showCompanyField:p,requireCompanyField:u,showApartmentField:h,requireApartmentField:g,showPhoneField:E,requirePhoneField:b}=(0,B.Oy)(),{showBillingFields:w,forcedBillingAddress:_,useBillingAsShipping:f}=(0,a.C)();return w||f?(e=((e,t)=>t?e===F?k:e:e===k?F:e)(e,_),t=((e,t)=>t?e===S?v:e:e===v?S:e)(t,_),(0,r.createElement)(d.FormStep,{id:"billing-fields",disabled:m,className:(0,n.A)("wc-block-checkout__billing-fields",i),title:e,description:t,showStepNumber:c},(0,r.createElement)(A,{showCompanyField:p,requireCompanyField:u,showApartmentField:h,requireApartmentField:g,showPhoneField:E,requirePhoneField:b}),s)):null}))},1094:()=>{},7259:()=>{}}]);