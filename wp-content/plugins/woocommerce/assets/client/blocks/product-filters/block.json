{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "woocommerce/product-filters", "version": "1.0.0", "title": "Product Filters (Experimental)", "description": "Let shoppers filter products displayed on the page.", "category": "woocommerce", "keywords": ["WooCommerce"], "supports": {"align": true, "color": {"background": true, "text": true, "heading": true, "enableContrastChecker": false, "button": true}, "multiple": true, "inserter": true, "interactivity": true, "typography": {"fontSize": true}}, "textdomain": "woocommerce", "usesContext": ["postId", "query", "queryId"], "viewScript": "wc-product-filters-frontend", "example": {}, "attributes": {"overlayIcon": {"type": "string", "default": "filter-icon-2"}, "overlayIconSize": {"type": "number"}, "overlayButtonType": {"type": "string", "default": "label-icon"}}}