"use strict";var __importDefault=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.ImageBlockEdit=void 0;const i18n_1=require("@wordpress/i18n"),components_1=require("@wordpress/components"),data_1=require("@wordpress/data"),classnames_1=__importDefault(require("classnames")),element_1=require("@wordpress/element"),icons_1=require("@wordpress/icons"),block_templates_1=require("@woocommerce/block-templates"),components_2=require("@woocommerce/components"),tracks_1=require("@woocommerce/tracks"),core_data_1=require("@wordpress/core-data"),place_holder_1=require("./place-holder"),block_slot_fill_1=require("../../../components/block-slot-fill"),map_upload_image_to_image_1=require("../../../utils/map-upload-image-to-image");function ImageBlockEdit({attributes:e,context:o}){var r,a;const{property:t,multiple:i}=e,[l,n]=(0,core_data_1.useEntityProp)("postType",o.postType,t),[_,m]=(0,element_1.useState)(!1),[c,s]=(0,element_1.useState)(!1),[d,p]=(0,element_1.useState)(null),u=(0,block_templates_1.useWooBlockProps)(e,{className:(0,classnames_1.default)({"has-images":Array.isArray(l)?l.length>0:Boolean(l)})}),{createErrorNotice:g}=(0,data_1.useDispatch)("core/notices");function v(e){return function(o){var r;if((0,tracks_1.recordEvent)(e),Array.isArray(o)){const e=o.filter((e=>e.id)).map((e=>({id:e.id,name:e.title,src:e.url,alt:e.alt})));(null===(r=o[0])||void 0===r?void 0:r.id)&&n([...l,...e])}else o.id&&n((0,map_upload_image_to_image_1.mapUploadImageToImage)(o))}}const f=null!==l&&(!Array.isArray(l)||l.length>0);return(0,element_1.createElement)("div",{...u},(0,element_1.createElement)("div",{className:"woocommerce-product-form__image-drop-zone"},_?(0,element_1.createElement)("div",{className:"woocommerce-product-form__remove-image-drop-zone"},(0,element_1.createElement)("span",null,(0,element_1.createElement)(icons_1.Icon,{icon:icons_1.trash,size:20,className:"icon-control"}),(0,i18n_1.__)("Drop here to remove","woocommerce")),(0,element_1.createElement)(components_1.DropZone,{onHTMLDrop:()=>s(!0),onDrop:()=>s(!0),label:(0,i18n_1.__)("Drop here to remove","woocommerce")})):(0,element_1.createElement)(block_slot_fill_1.SectionActions,null,(0,element_1.createElement)("div",{className:"woocommerce-product-form__media-uploader"},(0,element_1.createElement)(components_2.MediaUploader,{value:Array.isArray(l)?l.map((({id:e})=>e)):null!==(r=null==l?void 0:l.id)&&void 0!==r?r:void 0,multipleSelect:!!i&&"add",maxUploadFileSize:null===(a=window.productBlockEditorSettings)||void 0===a?void 0:a.maxUploadFileSize,onError:function(e){g((0,i18n_1.sprintf)((0,i18n_1.__)("Error uploading image:%1$s%2$s","woocommerce"),"\n",e.message))},onFileUploadChange:v("product_images_add_via_file_upload_area"),onMediaGalleryOpen:()=>{(0,tracks_1.recordEvent)("product_images_media_gallery_open")},onSelect:function(e){if((0,tracks_1.recordEvent)("product_images_add_via_media_library"),Array.isArray(e)){const o=e.map(map_upload_image_to_image_1.mapUploadImageToImage).filter((e=>null!==e));n(o)}else n((0,map_upload_image_to_image_1.mapUploadImageToImage)(e))},onUpload:v("product_images_add_via_drag_and_drop_upload"),label:"",buttonText:(0,i18n_1.__)("Choose an image","woocommerce")})))),f?(0,element_1.createElement)(components_2.ImageGallery,{allowDragging:!1,onDragStart:function(e){var o,r;if(Array.isArray(l)){const{id:a,dataset:t}=e.target;if(a)p(parseInt(a,10));else if(null==t?void 0:t.index){const e=parseInt(t.index,10);p(null!==(r=null===(o=l[e])||void 0===o?void 0:o.id)&&void 0!==r?r:null)}m((e=>!e))}},onDragEnd:function(){Array.isArray(l)&&(c&&d&&((0,tracks_1.recordEvent)("product_images_remove_image_button_click"),n(l.filter((e=>e.id!==d))),s(!1),p(null)),m((e=>!e)))},onOrderChange:function(e){if(Array.isArray(l)){const o=l.reduce(((e,o)=>({...e,[`${o.id}`]:o})),{}),r=e.filter((e=>{var r;return(null===(r=null==e?void 0:e.props)||void 0===r?void 0:r.id)in o})).map((e=>{var r;return o[null===(r=null==e?void 0:e.props)||void 0===r?void 0:r.id]}));(0,tracks_1.recordEvent)("product_images_change_image_order_via_image_gallery"),n(r)}},onReplace:function({replaceIndex:e,media:o}){if((0,tracks_1.recordEvent)("product_images_replace_image_button_click"),Array.isArray(l)){if(l.some((e=>o.id===e.id)))return;const r=(0,map_upload_image_to_image_1.mapUploadImageToImage)(o);if(r){const o=[...l];o[e]=r,n(o)}}else n((0,map_upload_image_to_image_1.mapUploadImageToImage)(o))},onRemove:function({removedItem:e}){if((0,tracks_1.recordEvent)("product_images_remove_image_button_click"),Array.isArray(l)){const o=l.filter((o=>String(o.id)!==e.props.id));n(o)}else n(null)},onSelectAsCover:()=>(0,tracks_1.recordEvent)("product_images_select_image_as_cover_button_click")},(Array.isArray(l)?l:[l]).map(((e,o)=>(0,element_1.createElement)(components_2.ImageGalleryItem,{key:e.id,alt:e.alt,src:e.src,id:`${e.id}`,isCover:i&&0===o})))):(0,element_1.createElement)(place_holder_1.PlaceHolder,{multiple:i}))}exports.ImageBlockEdit=ImageBlockEdit;