"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[2397],{4320:(e,r,o)=>{o.d(r,{Z:()=>v});var t=o(69307),a=o(65736),c=o(94333),l=o(69771),s=o(9818),n=o(92819),i=o(69596),m=o.n(i),d=o(86020),u=o(67221),p=o(81921),_=o(17844),h=o(10431);function y(e,r,o={}){if(!e||0===e.length)return null;const t=e.slice(0),a=t.pop();if(a.showFilters(r,o)){const e=(0,h.flattenFilters)(a.filters),o=r[a.param]||a.defaultValue||"all";return(0,n.find)(e,{value:o})}return y(t,r,o)}function w(e){return r=>(0,l.format)(e,r)}function b(e){if(e?.data?.intervals?.length>1){const r=e.data.intervals[0].date_start,o=e.data.intervals[e.data.intervals.length-1].date_end;if((0,p.containsLeapYear)(r,o))return!0}return!1}class f extends t.Component{shouldComponentUpdate(e){return e.isRequesting!==this.props.isRequesting||e.primaryData.isRequesting!==this.props.primaryData.isRequesting||e.secondaryData.isRequesting!==this.props.secondaryData.isRequesting||!(0,n.isEqual)(e.query,this.props.query)}getItemChartData(){const{primaryData:e,selectedChart:r}=this.props;return e.data.intervals.map((function(e){const o={};return e.subtotals.segments.forEach((function(e){if(e.segment_label){const t=o[e.segment_label]?e.segment_label+" (#"+e.segment_id+")":e.segment_label;o[e.segment_id]={label:t,value:e.subtotals[r.key]||0}}})),{date:(0,l.format)("Y-m-d\\TH:i:s",e.date_start),...o}}))}getTimeChartData(){const{query:e,primaryData:r,secondaryData:o,selectedChart:t,defaultDateRange:a}=this.props,c=(0,p.getIntervalForQuery)(e,a),{primary:s,secondary:n}=(0,p.getCurrentDates)(e,a);return function(e,r,o,t,a,c,s){const n=b(e),i=b(r),m=[...e.data.intervals],d=[...r.data.intervals],u=[];for(let e=0;e<m.length;e++){const r=m[e],_=(0,l.format)("Y-m-d\\TH:i:s",r.date_start),h=`${o.label} (${o.range})`,y=r.date_start,w=r.subtotals[c]||0,b=d[e],f=`${t.label} (${t.range})`;let v=(0,p.getPreviousDate)(r.date_start,o.after,t.after,a,s).format("YYYY-MM-DD HH:mm:ss"),g=b&&b.subtotals[c]||0;if("day"===s&&n&&!i&&d?.[e]){const o=new Date(r.date_start),t=new Date(d[e].date_start);(0,p.isLeapYear)(o.getFullYear())&&1===o.getMonth()&&29===o.getDate()&&2===t.getMonth()&&1===t.getDate()&&(v="-",g=0,d.splice(e,0,d[e]))}u.push({date:_,primary:{label:h,labelDate:y,value:w},secondary:{label:f,labelDate:v,value:g}})}return u}(r,o,s,n,e.compare,t.key,c)}getTimeChartTotals(){const{primaryData:e,secondaryData:r,selectedChart:o}=this.props;return{primary:(0,n.get)(e,["data","totals",o.key],null),secondary:(0,n.get)(r,["data","totals",o.key],null)}}renderChart(e,r,o,c){const{emptySearchResults:l,filterParam:s,interactiveLegend:n,itemsLabel:i,legendPosition:m,path:_,query:h,selectedChart:y,showHeaderControls:b,primaryData:f,defaultDateRange:v}=this.props,g=(0,p.getIntervalForQuery)(h,v),C=(0,p.getAllowedIntervalsForQuery)(h,v),x=(0,p.getDateFormatsForInterval)(g,f.data.intervals.length,{type:"php"}),S=l?(0,a.__)("No data for the current search","woocommerce"):(0,a.__)("No data for the selected date range","woocommerce"),{formatAmount:k,getCurrencyConfig:T}=this.context;return(0,t.createElement)(d.Chart,{allowedIntervals:C,data:o,dateParser:"%Y-%m-%dT%H:%M:%S",emptyMessage:S,filterParam:s,interactiveLegend:n,interval:g,isRequesting:r,itemsLabel:i,legendPosition:m,legendTotals:c,mode:e,path:_,query:h,screenReaderFormat:w(x.screenReaderFormat),showHeaderControls:b,title:y.label,tooltipLabelFormat:w(x.tooltipLabelFormat),tooltipTitle:"time-comparison"===e&&y.label||null,tooltipValueFormat:(0,u.getTooltipValueFormat)(y.type,k),chartType:(0,p.getChartTypeForQuery)(h),valueType:y.type,xFormat:w(x.xFormat),x2Format:w(x.x2Format),currency:T()})}renderItemComparison(){const{isRequesting:e,primaryData:r}=this.props;if(r.isError)return(0,t.createElement)(d.AnalyticsError,null);const o=e||r.isRequesting,a=this.getItemChartData();return this.renderChart("item-comparison",o,a)}renderTimeComparison(){const{isRequesting:e,primaryData:r,secondaryData:o}=this.props;if(!r||r.isError||o.isError)return(0,t.createElement)(d.AnalyticsError,null);const a=e||r.isRequesting||o.isRequesting,c=this.getTimeChartData(),l=this.getTimeChartTotals();return this.renderChart("time-comparison",a,c,l)}render(){const{mode:e}=this.props;return"item-comparison"===e?this.renderItemComparison():this.renderTimeComparison()}}f.contextType=_.CurrencyContext,f.propTypes={filters:m().array,isRequesting:m().bool,itemsLabel:m().string,limitProperties:m().array,mode:m().string,path:m().string.isRequired,primaryData:m().object,query:m().object.isRequired,secondaryData:m().object,selectedChart:m().shape({key:m().string.isRequired,label:m().string.isRequired,order:m().oneOf(["asc","desc"]),orderby:m().string,type:m().oneOf(["average","number","currency"]).isRequired}).isRequired},f.defaultProps={isRequesting:!1,primaryData:{data:{intervals:[]},isError:!1,isRequesting:!1},secondaryData:{data:{intervals:[]},isError:!1,isRequesting:!1}};const v=(0,c.compose)((0,s.withSelect)(((e,r)=>{const{charts:o,endpoint:t,filters:a,isRequesting:c,limitProperties:l,query:s,advancedFilters:i}=r,m=l||[t],d=y(a,s),p=(0,n.get)(d,["settings","param"]),_=r.mode||function(e,r){if(e&&r){const o=(0,n.get)(e,["settings","param"]);if(!o||Object.keys(r).includes(o))return(0,n.get)(e,["chartMode"])}return null}(d,s)||"time-comparison",{woocommerce_default_date_range:h}=e(u.SETTINGS_STORE_NAME).getSetting("wc_admin","wcAdminSettings"),w=e(u.REPORTS_STORE_NAME),b={mode:_,filterParam:p,defaultDateRange:h};if(c)return b;const f=m.some((e=>s[e]&&s[e].length));if(s.search&&!f)return{...b,emptySearchResults:!0};const v=o&&o.map((e=>e.key)),g=(0,u.getReportChartData)({endpoint:t,dataType:"primary",query:s,selector:w,limitBy:m,filters:a,advancedFilters:i,defaultDateRange:h,fields:v});if("item-comparison"===_)return{...b,primaryData:g};const C=(0,u.getReportChartData)({endpoint:t,dataType:"secondary",query:s,selector:w,limitBy:m,filters:a,advancedFilters:i,defaultDateRange:h,fields:v});return{...b,primaryData:g,secondaryData:C}})))(f)},15873:(e,r,o)=>{o.d(r,{O3:()=>i,be:()=>m,u8:()=>u});var t=o(65736),a=o(92694),c=o(9818),l=o(75606),s=o(3362);const{addCesSurveyForAnalytics:n}=(0,c.dispatch)(l.STORE_KEY),i=(0,a.applyFilters)("woocommerce_admin_coupons_report_charts",[{key:"orders_count",label:(0,t.__)("Discounted orders","woocommerce"),order:"desc",orderby:"orders_count",type:"number"},{key:"amount",label:(0,t.__)("Amount","woocommerce"),order:"desc",orderby:"amount",type:"currency"}]),m=(0,a.applyFilters)("woocommerce_admin_coupon_report_advanced_filters",{filters:{},title:(0,t._x)("Coupons match <select/> filters","A sentence describing filters for Coupons. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","woocommerce")}),d=[{label:(0,t.__)("All coupons","woocommerce"),value:"all"},{label:(0,t.__)("Single coupon","woocommerce"),value:"select_coupon",chartMode:"item-comparison",subFilters:[{component:"Search",value:"single_coupon",chartMode:"item-comparison",path:["select_coupon"],settings:{type:"coupons",param:"coupons",getLabels:s.hQ,labels:{placeholder:(0,t.__)("Type to search for a coupon","woocommerce"),button:(0,t.__)("Single Coupon","woocommerce")}}}]},{label:(0,t.__)("Comparison","woocommerce"),value:"compare-coupons",settings:{type:"coupons",param:"coupons",getLabels:s.hQ,labels:{title:(0,t.__)("Compare Coupon Codes","woocommerce"),update:(0,t.__)("Compare","woocommerce"),helpText:(0,t.__)("Check at least two coupon codes below to compare","woocommerce")},onClick:n}}];Object.keys(m.filters).length&&d.push({label:(0,t.__)("Advanced filters","woocommerce"),value:"advanced"});const u=(0,a.applyFilters)("woocommerce_admin_coupons_report_filters",[{label:(0,t.__)("Show","woocommerce"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>!0,filters:d}])},97269:(e,r,o)=>{o.d(r,{O3:()=>l,be:()=>n,u8:()=>s});var t=o(65736),a=o(92694),c=o(3362);const l=(0,a.applyFilters)("woocommerce_admin_downloads_report_charts",[{key:"download_count",label:(0,t.__)("Downloads","woocommerce"),type:"number"}]),s=(0,a.applyFilters)("woocommerce_admin_downloads_report_filters",[{label:(0,t.__)("Show","woocommerce"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>!0,filters:[{label:(0,t.__)("All downloads","woocommerce"),value:"all"},{label:(0,t.__)("Advanced filters","woocommerce"),value:"advanced"}]}]),n=(0,a.applyFilters)("woocommerce_admin_downloads_report_advanced_filters",{title:(0,t._x)("Downloads match <select/> filters","A sentence describing filters for Downloads. See screen shot for context: https://cloudup.com/ccxhyH2mEDg","woocommerce"),filters:{product:{labels:{add:(0,t.__)("Product","woocommerce"),placeholder:(0,t.__)("Search","woocommerce"),remove:(0,t.__)("Remove product filter","woocommerce"),rule:(0,t.__)("Select a product filter match","woocommerce"),title:(0,t.__)("<title>Product</title> <rule/> <filter/>","woocommerce"),filter:(0,t.__)("Select product","woocommerce")},rules:[{value:"includes",label:(0,t._x)("Includes","products","woocommerce")},{value:"excludes",label:(0,t._x)("Excludes","products","woocommerce")}],input:{component:"Search",type:"products",getLabels:c.oC}},customer:{labels:{add:(0,t.__)("Username","woocommerce"),placeholder:(0,t.__)("Search customer username","woocommerce"),remove:(0,t.__)("Remove customer username filter","woocommerce"),rule:(0,t.__)("Select a customer username filter match","woocommerce"),title:(0,t.__)("<title>Username</title> <rule/> <filter />","woocommerce"),filter:(0,t.__)("Select customer username","woocommerce")},rules:[{value:"includes",label:(0,t._x)("Includes","customer usernames","woocommerce")},{value:"excludes",label:(0,t._x)("Excludes","customer usernames","woocommerce")}],input:{component:"Search",type:"usernames",getLabels:c.jk}},order:{labels:{add:(0,t.__)("Order #","woocommerce"),placeholder:(0,t.__)("Search order number","woocommerce"),remove:(0,t.__)("Remove order number filter","woocommerce"),rule:(0,t.__)("Select an order number filter match","woocommerce"),title:(0,t.__)("<title>Order #</title> <rule/> <filter/>","woocommerce"),filter:(0,t.__)("Select order number","woocommerce")},rules:[{value:"includes",label:(0,t._x)("Includes","order numbers","woocommerce")},{value:"excludes",label:(0,t._x)("Excludes","order numbers","woocommerce")}],input:{component:"Search",type:"orders",getLabels:async e=>{const r=e.split(",");return await r.map((e=>({id:e,label:"#"+e})))}}},ip_address:{labels:{add:(0,t.__)("IP Address","woocommerce"),placeholder:(0,t.__)("Search IP address","woocommerce"),remove:(0,t.__)("Remove IP address filter","woocommerce"),rule:(0,t.__)("Select an IP address filter match","woocommerce"),title:(0,t.__)("<title>IP Address</title> <rule/> <filter/>","woocommerce"),filter:(0,t.__)("Select IP address","woocommerce")},rules:[{value:"includes",label:(0,t._x)("Includes","IP addresses","woocommerce")},{value:"excludes",label:(0,t._x)("Excludes","IP addresses","woocommerce")}],input:{component:"Search",type:"downloadIps",getLabels:async e=>{const r=e.split(",");return await r.map((e=>({id:e,label:e})))}}}}})},54605:(e,r,o)=>{o.d(r,{O3:()=>s,be:()=>i,u8:()=>n});var t=o(65736),a=o(92694),c=o(3362),l=o(88679);const s=(0,a.applyFilters)("woocommerce_admin_orders_report_charts",[{key:"orders_count",label:(0,t.__)("Orders","woocommerce"),type:"number"},{key:"net_revenue",label:(0,t.__)("Net sales","woocommerce"),order:"desc",orderby:"net_total",type:"currency"},{key:"avg_order_value",label:(0,t.__)("Average order value","woocommerce"),type:"currency"},{key:"avg_items_per_order",label:(0,t.__)("Average items per order","woocommerce"),order:"desc",orderby:"num_items_sold",type:"average"}]),n=(0,a.applyFilters)("woocommerce_admin_orders_report_filters",[{label:(0,t.__)("Show","woocommerce"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>!0,filters:[{label:(0,t.__)("All orders","woocommerce"),value:"all"},{label:(0,t.__)("Advanced filters","woocommerce"),value:"advanced"}]}]),i=(0,a.applyFilters)("woocommerce_admin_orders_report_advanced_filters",{title:(0,t._x)("Orders match <select/> filters","A sentence describing filters for Orders. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","woocommerce"),filters:{status:{labels:{add:(0,t.__)("Order status","woocommerce"),remove:(0,t.__)("Remove order status filter","woocommerce"),rule:(0,t.__)("Select an order status filter match","woocommerce"),title:(0,t.__)("<title>Order status</title> <rule/> <filter/>","woocommerce"),filter:(0,t.__)("Select an order status","woocommerce")},rules:[{value:"is",label:(0,t._x)("Is","order status","woocommerce")},{value:"is_not",label:(0,t._x)("Is Not","order status","woocommerce")}],input:{component:"SelectControl",options:Object.keys(l.rq).map((e=>({value:e,label:l.rq[e]})))}},product:{labels:{add:(0,t.__)("Product","woocommerce"),placeholder:(0,t.__)("Search products","woocommerce"),remove:(0,t.__)("Remove product filter","woocommerce"),rule:(0,t.__)("Select a product filter match","woocommerce"),title:(0,t.__)("<title>Product</title> <rule/> <filter/>","woocommerce"),filter:(0,t.__)("Select products","woocommerce")},rules:[{value:"includes",label:(0,t._x)("Includes","products","woocommerce")},{value:"excludes",label:(0,t._x)("Excludes","products","woocommerce")}],input:{component:"Search",type:"products",getLabels:c.oC}},variation:{labels:{add:(0,t.__)("Product variation","woocommerce"),placeholder:(0,t.__)("Search product variations","woocommerce"),remove:(0,t.__)("Remove product variation filter","woocommerce"),rule:(0,t.__)("Select a product variation filter match","woocommerce"),title:(0,t.__)("<title>Product variation</title> <rule/> <filter/>","woocommerce"),filter:(0,t.__)("Select variation","woocommerce")},rules:[{value:"includes",label:(0,t._x)("Includes","variations","woocommerce")},{value:"excludes",label:(0,t._x)("Excludes","variations","woocommerce")}],input:{component:"Search",type:"variations",getLabels:c.uC}},coupon:{labels:{add:(0,t.__)("Coupon code","woocommerce"),placeholder:(0,t.__)("Search coupons","woocommerce"),remove:(0,t.__)("Remove coupon filter","woocommerce"),rule:(0,t.__)("Select a coupon filter match","woocommerce"),title:(0,t.__)("<title>Coupon code</title> <rule/> <filter/>","woocommerce"),filter:(0,t.__)("Select coupon codes","woocommerce")},rules:[{value:"includes",label:(0,t._x)("Includes","coupon code","woocommerce")},{value:"excludes",label:(0,t._x)("Excludes","coupon code","woocommerce")}],input:{component:"Search",type:"coupons",getLabels:c.hQ}},customer_type:{labels:{add:(0,t.__)("Customer type","woocommerce"),remove:(0,t.__)("Remove customer filter","woocommerce"),rule:(0,t.__)("Select a customer filter match","woocommerce"),title:(0,t.__)("<title>Customer is</title> <filter/>","woocommerce"),filter:(0,t.__)("Select a customer type","woocommerce")},input:{component:"SelectControl",options:[{value:"new",label:(0,t.__)("New","woocommerce")},{value:"returning",label:(0,t.__)("Returning","woocommerce")}],defaultOption:"new"}},refunds:{labels:{add:(0,t.__)("Refund","woocommerce"),remove:(0,t.__)("Remove refund filter","woocommerce"),rule:(0,t.__)("Select a refund filter match","woocommerce"),title:(0,t.__)("<title>Refund</title> <filter/>","woocommerce"),filter:(0,t.__)("Select a refund type","woocommerce")},input:{component:"SelectControl",options:[{value:"all",label:(0,t.__)("All","woocommerce")},{value:"partial",label:(0,t.__)("Partially refunded","woocommerce")},{value:"full",label:(0,t.__)("Fully refunded","woocommerce")},{value:"none",label:(0,t.__)("None","woocommerce")}],defaultOption:"all"}},tax_rate:{labels:{add:(0,t.__)("Tax rate","woocommerce"),placeholder:(0,t.__)("Search tax rates","woocommerce"),remove:(0,t.__)("Remove tax rate filter","woocommerce"),rule:(0,t.__)("Select a tax rate filter match","woocommerce"),title:(0,t.__)("<title>Tax Rate</title> <rule/> <filter/>","woocommerce"),filter:(0,t.__)("Select tax rates","woocommerce")},rules:[{value:"includes",label:(0,t._x)("Includes","tax rate","woocommerce")},{value:"excludes",label:(0,t._x)("Excludes","tax rate","woocommerce")}],input:{component:"Search",type:"taxes",getLabels:c.FI}},attribute:{allowMultiple:!0,labels:{add:(0,t.__)("Product attribute","woocommerce"),placeholder:(0,t.__)("Search product attributes","woocommerce"),remove:(0,t.__)("Remove product attribute filter","woocommerce"),rule:(0,t.__)("Select a product attribute filter match","woocommerce"),title:(0,t.__)("<title>Product attribute</title> <rule/> <filter/>","woocommerce"),filter:(0,t.__)("Select attributes","woocommerce")},rules:[{value:"is",label:(0,t._x)("Is","product attribute","woocommerce")},{value:"is_not",label:(0,t._x)("Is Not","product attribute","woocommerce")}],input:{component:"ProductAttribute"}}}})},13740:(e,r,o)=>{o.d(r,{O3:()=>i,be:()=>u,u8:()=>p});var t=o(65736),a=o(92694),c=o(9818),l=o(75606),s=o(3362);const{addCesSurveyForAnalytics:n}=(0,c.dispatch)(l.STORE_KEY),i=(0,a.applyFilters)("woocommerce_admin_products_report_charts",[{key:"items_sold",label:(0,t.__)("Items sold","woocommerce"),order:"desc",orderby:"items_sold",type:"number"},{key:"net_revenue",label:(0,t.__)("Net sales","woocommerce"),order:"desc",orderby:"net_revenue",type:"currency"},{key:"orders_count",label:(0,t.__)("Orders","woocommerce"),order:"desc",orderby:"orders_count",type:"number"}]),m={label:(0,t.__)("Show","woocommerce"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>!0,filters:[{label:(0,t.__)("All products","woocommerce"),value:"all"},{label:(0,t.__)("Single product","woocommerce"),value:"select_product",chartMode:"item-comparison",subFilters:[{component:"Search",value:"single_product",chartMode:"item-comparison",path:["select_product"],settings:{type:"products",param:"products",getLabels:s.oC,labels:{placeholder:(0,t.__)("Type to search for a product","woocommerce"),button:(0,t.__)("Single product","woocommerce")}}}]},{label:(0,t.__)("Comparison","woocommerce"),value:"compare-products",chartMode:"item-comparison",settings:{type:"products",param:"products",getLabels:s.oC,labels:{helpText:(0,t.__)("Check at least two products below to compare","woocommerce"),placeholder:(0,t.__)("Search for products to compare","woocommerce"),title:(0,t.__)("Compare Products","woocommerce"),update:(0,t.__)("Compare","woocommerce")},onClick:n}}]},d={showFilters:e=>"single_product"===e.filter&&!!e.products&&e["is-variable"],staticParams:["filter","products","chartType","paged","per_page"],param:"filter-variations",filters:[{label:(0,t.__)("All variations","woocommerce"),chartMode:"item-comparison",value:"all"},{label:(0,t.__)("Single variation","woocommerce"),value:"select_variation",subFilters:[{component:"Search",value:"single_variation",path:["select_variation"],settings:{type:"variations",param:"variations",getLabels:s.uC,labels:{placeholder:(0,t.__)("Type to search for a variation","woocommerce"),button:(0,t.__)("Single variation","woocommerce")}}}]},{label:(0,t.__)("Comparison","woocommerce"),chartMode:"item-comparison",value:"compare-variations",settings:{type:"variations",param:"variations",getLabels:s.uC,labels:{helpText:(0,t.__)("Check at least two variations below to compare","woocommerce"),placeholder:(0,t.__)("Search for variations to compare","woocommerce"),title:(0,t.__)("Compare Variations","woocommerce"),update:(0,t.__)("Compare","woocommerce")}}}]},u=(0,a.applyFilters)("woocommerce_admin_products_report_advanced_filters",{filters:{},title:(0,t._x)("Products Match <select/> Filters","A sentence describing filters for Products. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","woocommerce")});Object.keys(u.filters).length&&(m.filters.push({label:(0,t.__)("Advanced Filters","woocommerce"),value:"advanced"}),d.filters.push({label:(0,t.__)("Advanced Filters","woocommerce"),value:"advanced"}));const p=(0,a.applyFilters)("woocommerce_admin_products_report_filters",[m,d])},27783:(e,r,o)=>{o.d(r,{O3:()=>c,be:()=>l,u8:()=>n});var t=o(65736),a=o(92694);const c=(0,a.applyFilters)("woocommerce_admin_revenue_report_charts",[{key:"gross_sales",label:(0,t.__)("Gross sales","woocommerce"),order:"desc",orderby:"gross_sales",type:"currency",isReverseTrend:!1},{key:"refunds",label:(0,t.__)("Returns","woocommerce"),order:"desc",orderby:"refunds",type:"currency",isReverseTrend:!0},{key:"coupons",label:(0,t.__)("Coupons","woocommerce"),order:"desc",orderby:"coupons",type:"currency",isReverseTrend:!1},{key:"net_revenue",label:(0,t.__)("Net sales","woocommerce"),orderby:"net_revenue",type:"currency",isReverseTrend:!1,labelTooltipText:(0,t.__)("Full refunds are not deducted from tax or net sales totals","woocommerce")},{key:"taxes",label:(0,t.__)("Taxes","woocommerce"),order:"desc",orderby:"taxes",type:"currency",isReverseTrend:!1,labelTooltipText:(0,t.__)("Full refunds are not deducted from tax or net sales totals","woocommerce")},{key:"shipping",label:(0,t.__)("Shipping","woocommerce"),orderby:"shipping",type:"currency",isReverseTrend:!1},{key:"total_sales",label:(0,t.__)("Total sales","woocommerce"),order:"desc",orderby:"total_sales",type:"currency",isReverseTrend:!1}]),l=(0,a.applyFilters)("woocommerce_admin_revenue_report_advanced_filters",{filters:{},title:(0,t._x)("Revenue Matches <select/> Filters","A sentence describing filters for Revenue. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","woocommerce")}),s=[];Object.keys(l.filters).length&&(s.push({label:(0,t.__)("All Revenue","woocommerce"),value:"all"}),s.push({label:(0,t.__)("Advanced Filters","woocommerce"),value:"advanced"}));const n=(0,a.applyFilters)("woocommerce_admin_revenue_report_filters",[{label:(0,t.__)("Show","woocommerce"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>s.length>0,filters:s}])},22881:(e,r,o)=>{o.d(r,{O3:()=>d,be:()=>u,u8:()=>_});var t=o(65736),a=o(92694),c=o(75606),l=o(67221),s=o(9818),n=o(3362),i=o(86169);const{addCesSurveyForAnalytics:m}=(0,s.dispatch)(c.STORE_KEY),d=(0,a.applyFilters)("woocommerce_admin_taxes_report_charts",[{key:"total_tax",label:(0,t.__)("Total tax","woocommerce"),order:"desc",orderby:"total_tax",type:"currency"},{key:"order_tax",label:(0,t.__)("Order tax","woocommerce"),order:"desc",orderby:"order_tax",type:"currency"},{key:"shipping_tax",label:(0,t.__)("Shipping tax","woocommerce"),order:"desc",orderby:"shipping_tax",type:"currency"},{key:"orders_count",label:(0,t.__)("Orders","woocommerce"),order:"desc",orderby:"orders_count",type:"number"}]),u=(0,a.applyFilters)("woocommerce_admin_taxes_report_advanced_filters",{filters:{},title:(0,t._x)("Taxes match <select/> filters","A sentence describing filters for Taxes. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","woocommerce")}),p=[{label:(0,t.__)("All taxes","woocommerce"),value:"all"},{label:(0,t.__)("Comparison","woocommerce"),value:"compare-taxes",chartMode:"item-comparison",settings:{type:"taxes",param:"taxes",getLabels:(0,n.qc)(l.NAMESPACE+"/taxes",(e=>({id:e.id,key:e.id,label:(0,i.I)(e)}))),labels:{helpText:(0,t.__)("Check at least two tax codes below to compare","woocommerce"),placeholder:(0,t.__)("Search for tax codes to compare","woocommerce"),title:(0,t.__)("Compare Tax Codes","woocommerce"),update:(0,t.__)("Compare","woocommerce")},onClick:m}}];Object.keys(u.filters).length&&p.push({label:(0,t.__)("Advanced filters","woocommerce"),value:"advanced"});const _=(0,a.applyFilters)("woocommerce_admin_taxes_report_filters",[{label:(0,t.__)("Show","woocommerce"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>!0,filters:p}])},86169:(e,r,o)=>{o.d(r,{I:()=>a});var t=o(65736);function a(e){return[e.country,e.state,e.name||(0,t.__)("TAX","woocommerce"),e.priority].map((e=>e.toString().toUpperCase().trim())).filter(Boolean).join("-")}},13469:(e,r,o)=>{o.r(r),o.d(r,{default:()=>q});var t=o(69307),a=o(65736),c=o(80225),l=o(65784),s=o(26280),n=o(69596),i=o.n(n),m=o(55609),d=o(86020),u=o(67221),p=o(81921),_=o(14599),h=o(66143),y=o(10431),w=o(74617),b=o(14812),f=o(4320);class v extends t.Component{constructor(...e){super(...e),(0,h.Z)(this,"handleChartClick",(()=>{const{selectedChart:e}=this.props;(0,y.getHistory)().push(this.getChartPath(e))}))}getChartPath(e){return(0,y.getNewPath)({chart:e.key},"/analytics/"+e.endpoint,(0,y.getPersistedQuery)())}render(){const{charts:e,endpoint:r,path:o,query:c,selectedChart:l,filters:s}=this.props;return l?(0,t.createElement)("div",{role:"presentation",className:"woocommerce-dashboard__chart-block-wrapper",onClick:this.handleChartClick},(0,t.createElement)(m.Card,{className:"woocommerce-dashboard__chart-block"},(0,t.createElement)(m.CardHeader,null,(0,t.createElement)(b.Text,{as:"h3",size:16,weight:600,color:"#23282d"},l.label)),(0,t.createElement)(m.CardBody,{size:null},(0,t.createElement)("a",{className:"screen-reader-text",href:(0,w.getAdminLink)(this.getChartPath(l))},(0,a.sprintf)((0,a.__)("%s Report","woocommerce"),l.label)),(0,t.createElement)(f.Z,{charts:e,endpoint:r,query:c,interactiveLegend:!1,legendPosition:"bottom",path:o,selectedChart:l,showHeaderControls:!1,filters:s})))):null}}v.propTypes={charts:i().array,endpoint:i().string.isRequired,path:i().string.isRequired,query:i().object.isRequired,selectedChart:i().object.isRequired};const g=v;var C=o(92694),x=o(54605),S=o(13740),k=o(27783),T=o(15873),E=o(22881),R=o(97269);const F={revenue:k.O3,orders:x.O3,products:S.O3,coupons:T.O3,taxes:E.O3,downloads:R.O3},O=[{label:(0,a.__)("Total sales","woocommerce"),report:"revenue",key:"total_sales"},{label:(0,a.__)("Net sales","woocommerce"),report:"revenue",key:"net_revenue"},{label:(0,a.__)("Orders","woocommerce"),report:"orders",key:"orders_count"},{label:(0,a.__)("Average order value","woocommerce"),report:"orders",key:"avg_order_value"},{label:(0,a.__)("Items sold","woocommerce"),report:"products",key:"items_sold"},{label:(0,a.__)("Returns","woocommerce"),report:"revenue",key:"refunds"},{label:(0,a.__)("Discounted orders","woocommerce"),report:"coupons",key:"orders_count"},{label:(0,a.__)("Gross discounted","woocommerce"),report:"coupons",key:"amount"},{label:(0,a.__)("Total tax","woocommerce"),report:"taxes",key:"total_tax"},{label:(0,a.__)("Order tax","woocommerce"),report:"taxes",key:"order_tax"},{label:(0,a.__)("Shipping tax","woocommerce"),report:"taxes",key:"shipping_tax"},{label:(0,a.__)("Shipping","woocommerce"),report:"revenue",key:"shipping"},{label:(0,a.__)("Downloads","woocommerce"),report:"downloads",key:"download_count"}],A=(0,C.applyFilters)("woocommerce_admin_dashboard_charts_filter",O.map((e=>({...F[e.report].find((r=>r.key===e.key)),label:e.label,endpoint:e.report})))),P=e=>{const{controls:r,hiddenBlocks:o,isFirst:n,isLast:i,onMove:h,onRemove:y,onTitleBlur:w,onTitleChange:b,onToggleHiddenBlock:f,path:v,title:C,titleInput:x,filters:S,defaultDateRange:k}=e,{updateUserPreferences:T,...E}=(0,u.useUserPreferences)(),[R,F]=(0,t.useState)(E.dashboard_chart_type||"line"),[O,P]=(0,t.useState)(E.dashboard_chart_interval||"day"),q={...e.query,chartType:R,interval:O},D=e=>()=>{F(e),T({dashboard_chart_type:e}),(0,_.recordEvent)("dash_charts_type_toggle",{chart_type:e})};return(0,t.createElement)("div",{className:"woocommerce-dashboard__dashboard-charts"},(0,t.createElement)(d.SectionHeader,{title:C||(0,a.__)("Charts","woocommerce"),menu:(0,t.createElement)(d.EllipsisMenu,{label:(0,a.__)("Choose which charts to display","woocommerce"),renderContent:({onToggle:e})=>(0,t.createElement)(t.Fragment,null,(0,t.createElement)(d.MenuTitle,null,(0,a.__)("Charts","woocommerce")),(({hiddenBlocks:e,onToggleHiddenBlock:r})=>A.map((o=>{const a=o.endpoint+"_"+o.key,c=!e.includes(a);return(0,t.createElement)(d.MenuItem,{checked:c,isCheckbox:!0,isClickable:!0,key:o.endpoint+"_"+o.key,onInvoke:()=>{r(a)(),(0,_.recordEvent)("dash_charts_chart_toggle",{status:c?"off":"on",key:a})}},o.label)})))({hiddenBlocks:o,onToggleHiddenBlock:f}),(0,t.createElement)(r,{onToggle:e,onMove:h,onRemove:y,isFirst:n,isLast:i,onTitleBlur:w,onTitleChange:b,titleInput:x}))}),className:"has-interval-select"},(({chartInterval:e,setInterval:r,query:o,defaultDateRange:c})=>{const l=(0,p.getAllowedIntervalsForQuery)(o,c);if(!l||l.length<1)return null;const s={hour:(0,a.__)("By hour","woocommerce"),day:(0,a.__)("By day","woocommerce"),week:(0,a.__)("By week","woocommerce"),month:(0,a.__)("By month","woocommerce"),quarter:(0,a.__)("By quarter","woocommerce"),year:(0,a.__)("By year","woocommerce")};return(0,t.createElement)(m.SelectControl,{className:"woocommerce-chart__interval-select",value:e,options:l.map((e=>({value:e,label:s[e]}))),"aria-label":"Chart period",onChange:r})})({chartInterval:O,setInterval:e=>{P(e),T({dashboard_chart_interval:e}),(0,_.recordEvent)("dash_charts_interval",{interval:e})},query:q,defaultDateRange:k}),(0,t.createElement)(m.NavigableMenu,{className:"woocommerce-chart__types",orientation:"horizontal",role:"menubar"},(0,t.createElement)(m.Button,{className:(0,c.Z)("woocommerce-chart__type-button",{"woocommerce-chart__type-button-selected":!q.chartType||"line"===q.chartType}),title:(0,a.__)("Line chart","woocommerce"),"aria-checked":"line"===q.chartType,role:"menuitemradio",tabIndex:"line"===q.chartType?0:-1,onClick:D("line")},(0,t.createElement)(l.Z,null)),(0,t.createElement)(m.Button,{className:(0,c.Z)("woocommerce-chart__type-button",{"woocommerce-chart__type-button-selected":"bar"===q.chartType}),title:(0,a.__)("Bar chart","woocommerce"),"aria-checked":"bar"===q.chartType,role:"menuitemradio",tabIndex:"bar"===q.chartType?0:-1,onClick:D("bar")},(0,t.createElement)(s.Z,null)))),(({hiddenBlocks:e,path:r,query:o,filters:a})=>{const c=A.reduce(((e,r)=>(void 0===e[r.endpoint]&&(e[r.endpoint]=[]),e[r.endpoint].push(r),e)),{});return(0,t.createElement)("div",{className:"woocommerce-dashboard__columns"},A.map((l=>e.includes(l.endpoint+"_"+l.key)?null:(0,t.createElement)(g,{charts:c[l.endpoint],endpoint:l.endpoint,key:l.endpoint+"_"+l.key,path:r,query:o,selectedChart:l,filters:a}))))})({hiddenBlocks:o,path:v,query:q,filters:S}))};P.propTypes={path:i().string.isRequired,query:i().object.isRequired,defaultDateRange:i().string.isRequired};const q=P},3362:(e,r,o)=>{o.d(r,{FI:()=>w,V1:()=>b,YC:()=>p,hQ:()=>_,jk:()=>h,oC:()=>y,qc:()=>u,uC:()=>f});var t=o(65736),a=o(96483),c=o(86989),l=o.n(c),s=o(92819),n=o(10431),i=o(67221),m=o(86169),d=o(88679);function u(e,r=s.identity){return function(o="",t){const c="function"==typeof e?e(t):e,s=(0,n.getIdsFromQuery)(o);if(s.length<1)return Promise.resolve([]);const i={include:s.join(","),per_page:s.length};return l()({path:(0,a.addQueryArgs)(c,i)}).then((e=>e.map(r)))}}u(i.NAMESPACE+"/products/attributes",(e=>({key:e.id,label:e.name})));const p=u(i.NAMESPACE+"/products/categories",(e=>({key:e.id,label:e.name}))),_=u(i.NAMESPACE+"/coupons",(e=>({key:e.id,label:e.code}))),h=u(i.NAMESPACE+"/customers",(e=>({key:e.id,label:e.name}))),y=u(i.NAMESPACE+"/products",(e=>({key:e.id,label:e.name}))),w=u(i.NAMESPACE+"/taxes",(e=>({key:e.id,label:(0,m.I)(e)})));function b({attributes:e,name:r}){const o=(0,d.O3)("variationTitleAttributesSeparator"," - ");if(r&&r.indexOf(o)>-1)return r;const a=(e||[]).map((({name:e,option:r})=>(r||(e=e.charAt(0).toUpperCase()+e.slice(1),r=(0,t.sprintf)((0,t.__)("Any %s","woocommerce"),e)),r))).join(", ");return a?r+o+a:r}const f=u((({products:e})=>e?i.NAMESPACE+`/products/${e}/variations`:i.NAMESPACE+"/variations"),(e=>({key:e.id,label:b(e)})))},65784:(e,r,o)=>{r.Z=function(e){var r=e.size,o=void 0===r?24:r,t=e.onClick,s=(e.icon,e.className),n=function(e,r){if(null==e)return{};var o,t,a=function(e,r){if(null==e)return{};var o,t,a={},c=Object.keys(e);for(t=0;t<c.length;t++)o=c[t],0<=r.indexOf(o)||(a[o]=e[o]);return a}(e,r);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(e);for(t=0;t<c.length;t++)o=c[t],0<=r.indexOf(o)||Object.prototype.propertyIsEnumerable.call(e,o)&&(a[o]=e[o])}return a}(e,c),i=["gridicon","gridicons-line-graph",s,!1,!1,!1].filter(Boolean).join(" ");return a.default.createElement("svg",l({className:i,height:o,width:o,onClick:t},n,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),a.default.createElement("g",null,a.default.createElement("path",{d:"M3 19h18v2H3zm3-3c1.1 0 2-.9 2-2 0-.5-.2-1-.5-1.3L8.8 10H9c.5 0 1-.2 1.3-.5l2.7 1.4v.1c0 1.1.9 2 2 2s2-.9 2-2c0-.5-.2-.9-.5-1.3L17.8 7h.2c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2c0 .5.2 1 .5 1.3L15.2 9H15c-.5 0-1 .2-1.3.5L11 8.2V8c0-1.1-.9-2-2-2s-2 .9-2 2c0 .5.2 1 .5 1.3L6.2 12H6c-1.1 0-2 .9-2 2s.9 2 2 2z"})))};var t,a=(t=o(99196))&&t.__esModule?t:{default:t},c=["size","onClick","icon","className"];function l(){return l=Object.assign?Object.assign.bind():function(e){for(var r,o=1;o<arguments.length;o++)for(var t in r=arguments[o])Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t]);return e},l.apply(this,arguments)}},26280:(e,r,o)=>{r.Z=function(e){var r=e.size,o=void 0===r?24:r,t=e.onClick,s=(e.icon,e.className),n=function(e,r){if(null==e)return{};var o,t,a=function(e,r){if(null==e)return{};var o,t,a={},c=Object.keys(e);for(t=0;t<c.length;t++)o=c[t],0<=r.indexOf(o)||(a[o]=e[o]);return a}(e,r);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(e);for(t=0;t<c.length;t++)o=c[t],0<=r.indexOf(o)||Object.prototype.propertyIsEnumerable.call(e,o)&&(a[o]=e[o])}return a}(e,c),i=["gridicon","gridicons-stats-alt",s,!1,!1,!!function(e){return 0==e%18}(o)&&"needs-offset-y"].filter(Boolean).join(" ");return a.default.createElement("svg",l({className:i,height:o,width:o,onClick:t},n,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),a.default.createElement("g",null,a.default.createElement("path",{d:"M21 21H3v-2h18v2zM8 10H4v7h4v-7zm6-7h-4v14h4V3zm6 3h-4v11h4V6z"})))};var t,a=(t=o(99196))&&t.__esModule?t:{default:t},c=["size","onClick","icon","className"];function l(){return l=Object.assign?Object.assign.bind():function(e){for(var r,o=1;o<arguments.length;o++)for(var t in r=arguments[o])Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t]);return e},l.apply(this,arguments)}}}]);