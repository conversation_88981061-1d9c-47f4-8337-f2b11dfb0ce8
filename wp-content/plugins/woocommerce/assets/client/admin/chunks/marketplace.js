"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[9669],{13608:(e,t,o)=>{o.d(t,{Z:()=>a});var r=o(69307),c=o(70444);const a=(0,r.createElement)(c.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(c.<PERSON>,{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"}))},45842:(e,t,o)=>{o.d(t,{Z:()=>a});var r=o(69307),c=o(70444);const a=(0,r.createElement)(c.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(c<PERSON><PERSON>,{d:"M10.6 6L9.4 7l4.6 5-4.6 5 1.2 1 5.4-6z"}))},70902:(e,t,o)=>{o.d(t,{Z:()=>a});var r=o(69307),c=o(70444);const a=(0,r.createElement)(c.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(c.Path,{d:"M6.5 12.4L12 8l5.5 4.4-.9 1.2L12 10l-4.5 3.6-1-1.2z"}))},95295:(e,t,o)=>{o.d(t,{Z:()=>a});var r=o(69307),c=o(70444);const a=(0,r.createElement)(c.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(c.Path,{d:"M12 3.2c-4.8 0-8.8 3.9-8.8 8.8 0 4.8 3.9 8.8 8.8 8.8 4.8 0 8.8-3.9 8.8-8.8 0-4.8-4-8.8-8.8-8.8zm0 16c-4 0-7.2-3.3-7.2-7.2C4.8 8 8 4.8 12 4.8s7.2 3.3 7.2 7.2c0 4-3.2 7.2-7.2 7.2zM11 17h2v-6h-2v6zm0-8h2V7h-2v2z"}))},77843:(e,t,o)=>{o.d(t,{Z:()=>a});var r=o(69307),c=o(70444);const a=(0,r.createElement)(c.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(c.Path,{d:"M13 19h-2v-2h2v2zm0-6h-2v-2h2v2zm0-6h-2V5h2v2z"}))},78276:(e,t,o)=>{o.d(t,{Z:()=>a});var r=o(69307),c=o(70444);const a=(0,r.createElement)(c.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(c.Path,{fillRule:"evenodd",d:"M6.5 8a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0zM8 5a3 3 0 100 6 3 3 0 000-6zm6.5 11a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0zm1.5-3a3 3 0 100 6 3 3 0 000-6zM5.47 17.41a.75.75 0 001.06 1.06L18.47 6.53a.75.75 0 10-1.06-1.06L5.47 17.41z",clipRule:"evenodd"}))},1642:(e,t,o)=>{o.d(t,{Z:()=>p});var r=o(69307),c=o(80225),a=o(95295),n=o(69441),s=o(78276),i=o(60247),l=o(353),m=o(42058);const u={info:a.Z,check:n.Z,percent:s.Z};function p(e){const{id:t,description:o,children:a,icon:n,isDismissible:s=!0,variant:p="info",onClose:d,onLoad:_}=e,[w,g]=(0,r.useState)("true"!==localStorage.getItem(`wc-marketplaceNoticeClosed-${t}`));if((0,r.useEffect)((()=>{w&&"function"==typeof _&&_()}),[w]),!w)return null;const y=(0,c.Z)("woocommerce-marketplace__notice",`woocommerce-marketplace__notice--${p}`,{"is-dismissible":s}),v=u[n||"info"],h=(0,c.Z)("woocommerce-marketplace__notice-icon",`woocommerce-marketplace__notice-icon--${p}`);return(0,r.createElement)("div",{className:y},n&&(0,r.createElement)("span",{className:h},(0,r.createElement)(i.Z,{icon:v})),(0,r.createElement)("div",{className:"woocommerce-marketplace__notice-content"},(0,r.createElement)("p",{className:"woocommerce-marketplace__notice-description",dangerouslySetInnerHTML:(0,m.ZP)(o)}),a&&(0,r.createElement)("div",{className:"woocommerce-marketplace__notice-children"},a)),s&&(0,r.createElement)("button",{className:"woocommerce-marketplace__notice-close","aria-label":"Close",onClick:()=>{g(!1),localStorage.setItem(`wc-marketplaceNoticeClosed-${t}`,"true"),"function"==typeof d&&d()}},(0,r.createElement)(i.Z,{icon:l.Z})))}},26635:(e,t,o)=>{o.d(t,{Z:()=>c});var r=o(69307);function c(){return(0,r.createElement)("svg",{width:"72",height:"60",viewBox:"0 0 72 60",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("g",{clipPath:"url(#clip0_4074_10418)"},(0,r.createElement)("path",{d:"M68.5301 33.3144C68.0263 32.1006 66.3348 32.344 65.8443 31.1636C65.3538 29.9832 66.7251 28.9562 66.2213 27.7458C65.7175 26.5354 64.0259 26.7755 63.5355 25.5951C63.045 24.4147 64.4163 23.3877 63.9125 22.1773C63.4087 20.9669 61.7171 21.207 61.2267 20.0266C60.7362 18.8462 62.1075 17.8192 61.6037 16.6088C61.0999 15.395 59.4083 15.6385 58.9179 14.4581C58.4274 13.2777 59.7987 12.2507 59.2949 11.0403C58.7911 9.82652 57.0995 10.0699 56.6091 8.88955C56.1186 7.70915 57.4899 6.68214 56.9861 5.47174C56.4823 4.26134 54.7907 4.50142 54.3003 3.32102C53.8465 2.22733 55.0476 1.11696 54.8274 -0.00341797L0 22.5941C0.5038 23.8079 2.19537 23.5644 2.68582 24.7448C3.17627 25.9252 1.805 26.9522 2.3088 28.1626C2.8126 29.373 4.50417 29.133 4.99462 30.3134C5.48508 31.4937 4.11381 32.5208 4.61761 33.7312C5.12141 34.9416 6.81297 34.7015 7.30343 35.8819C7.79388 37.0623 6.42261 38.0893 6.92641 39.2997C7.43021 40.5134 9.12178 40.27 9.61223 41.4504C10.1027 42.6308 8.73142 43.6578 9.23522 44.8682C9.73902 46.0786 11.4306 45.8385 11.921 47.0189C12.4115 48.1993 11.0402 49.2263 11.544 50.4367C12.0478 51.6471 13.7394 51.4071 14.2298 52.5874C14.6836 53.6811 13.4825 54.7915 13.7027 55.9119L28.1928 49.9232L68.5368 33.3177L68.5301 33.3144Z",fill:"#674399"}),(0,r.createElement)("path",{d:"M13.696 55.912L28.1861 49.9234L52.3851 39.9634H7.46021C8.17086 40.4802 9.23852 40.5569 9.60886 41.4539C10.0993 42.6343 8.72805 43.6613 9.23185 44.8717C9.73565 46.0821 11.4272 45.842 11.9177 47.0224C12.4081 48.2028 11.0368 49.2298 11.5406 50.4402C12.0444 51.6506 13.736 51.4105 14.2265 52.5909C14.6802 53.6846 13.4791 54.795 13.6993 55.9154L13.696 55.912Z",fill:"#3C2861"}),(0,r.createElement)("path",{d:"M63.8523 41.9907C63.8523 37.4925 67.499 33.848 71.9998 33.848V23.988H17.873V33.848C22.3739 33.848 26.0206 37.4925 26.0206 41.9907C26.0206 46.4889 22.3739 50.1334 17.873 50.1334V59.9934H71.9998V50.1334C67.499 50.1334 63.8523 46.4889 63.8523 41.9907Z",fill:"#BEA0F2"}),(0,r.createElement)("path",{d:"M35.2527 37.676C35.2527 35.2051 37.0143 33.2878 39.6968 33.2878C42.3793 33.2878 44.1643 35.2051 44.1643 37.676C44.1643 40.1468 42.4026 42.0107 39.6968 42.0107C36.991 42.0107 35.2527 40.1201 35.2527 37.676ZM41.7954 37.676C41.7954 36.2288 40.9046 35.3385 39.6935 35.3385C38.4823 35.3385 37.6182 36.2288 37.6182 37.676C37.6182 39.1231 38.509 39.9601 39.6935 39.9601C40.8779 39.9601 41.7954 39.0664 41.7954 37.676ZM37.9852 51.0704L49.1789 33.5513H51.1774L39.9537 51.0704H37.9819H37.9852ZM44.8983 47.0524C44.8983 44.5849 46.6566 42.641 49.3391 42.641C52.0215 42.641 53.8065 44.5849 53.8065 47.0524C53.8065 49.5199 52.0182 51.3872 49.3391 51.3872C46.6599 51.3872 44.8983 49.4966 44.8983 47.0524ZM51.441 47.0524C51.441 45.6053 50.5468 44.715 49.3357 44.715C48.1246 44.715 47.2605 45.6053 47.2605 47.0524C47.2605 48.4996 48.1279 49.3365 49.3357 49.3365C50.5435 49.3365 51.441 48.4696 51.441 47.0524Z",fill:"#674399"})),(0,r.createElement)("defs",null,(0,r.createElement)("clipPath",{id:"clip0_4074_10418"},(0,r.createElement)("rect",{width:"72",height:"60",fill:"white"}))))}},60501:(e,t,o)=>{o.d(t,{Z:()=>l});var r=o(69307),c=o(55609),a=o(65736),n=o(14599),s=o(42058);const i={percent:o(26635).Z},l=({promotion:e})=>{var t,o;const l=window.location.pathname+window.location.search,m=()=>JSON.parse(localStorage.getItem("wc-marketplaceDismissedPromos")||"[]"),[u,p]=(0,r.useState)(!m().includes(l));if((0,r.useEffect)((()=>{u&&(0,n.recordEvent)("marketplace_promotion_viewed",{path:l,format:"promo-card"})}),[u]),!u)return null;const d="promo-card"+(e.style?` ${e.style}`:""),_=(0,r.createElement)("div",{className:"promo-content"},(0,r.createElement)("h2",{className:"promo-title"},e.title?.en_US),(0,r.createElement)("div",{className:"promo-text",dangerouslySetInnerHTML:(0,s.ZP)(e.content?.en_US)})),w=(0,r.createElement)("div",{className:"promo-links"},(0,r.createElement)(c.Button,{className:"promo-cta",href:null!==(t=e.cta_link)&&void 0!==t?t:"",onClick:()=>((0,n.recordEvent)("marketplace_promotion_actioned",{path:l,target_uri:e.cta_link,format:"promo-card"}),!0)},null!==(o=e.cta_label?.en_US)&&void 0!==o?o:""),(0,r.createElement)(c.Button,{className:"promo-cta-link",onClick:()=>{p(!1),localStorage.setItem("wc-marketplaceDismissedPromos",JSON.stringify(m().concat(l))),(0,n.recordEvent)("marketplace_promotion_dismissed",{path:l,format:"promo-card"})}},(0,a.__)("Dismiss","woocommerce")));function g(){if(e.icon&&Object.hasOwn(i,e.icon)){const t=i[e.icon];return t?(0,r.createElement)("div",{className:"promo-image"},(0,r.createElement)(t)):null}return null}return(0,r.createElement)("div",{className:d},"has-background"===e?.style?(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"promo-content-links"},_,w),g()):(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"promo-content-image"},_,g()),w))}},19798:(e,t,o)=>{o.d(t,{Z:()=>i});var r=o(69307),c=o(14599),a=o(88679),n=o(1642),s=o(60501);const i=({format:e})=>{var t;if(!window?.wcMarketplace?.promotions||!Array.isArray(window?.wcMarketplace?.promotions))return null;const o=(null!==(t=window?.wcMarketplace?.promotions)&&void 0!==t?t:[]).filter((t=>t.format===e)),i=new URLSearchParams(window.location.search),l=i.get("page"),m=Date.now(),u=decodeURIComponent(i.get("path")||""),p=i.get("tab"),d=window.location.pathname+window.location.search,_=()=>{(0,c.recordEvent)("marketplace_promotion_viewed",{path:d,format:e})},w=()=>{(0,c.recordEvent)("marketplace_promotion_dismissed",{path:d,format:e})};return(0,r.createElement)(r.Fragment,null,o.map(((e,t)=>{if(!e.pages)return null;if(!e.pages.some((e=>{if(e.pathname)return e.pathname===d;if(!e.path)return!1;const t=e=>e.startsWith("/")?e:`/${e}`,o=t(e.path),r=t(u);return e.page===l&&o===r&&(e.tab?p:!p)})))return null;const o=new Date(e.date_from_gmt).getTime(),c=new Date(e.date_to_gmt).getTime();return m<o||m>c?null:"promo-card"===e.format?(0,r.createElement)(s.Z,{key:t,promotion:e}):"notice"===e.format&&e?.content?(0,r.createElement)(n.Z,{key:t,id:null!==(i=e.menu_item_id)&&void 0!==i?i:`promotion-${t}`,description:e.content[a.MV.userLocale]||e.content.en_US,variant:e.style?e.style:"info",icon:e?.icon||"",isDismissible:e.is_dismissible||!1,onLoad:_,onClose:w}):null;var i})))}},6820:(e,t,o)=>{o.r(t),o.d(t,{default:()=>Wt});var r=o(69307),c=o(88679);const a=(0,r.createContext)({isLoading:!1,setIsLoading:()=>{},selectedTab:"",setSelectedTab:()=>{},isProductInstalled:()=>!1,addInstalledProduct:()=>{},hasBusinessServices:!1,setHasBusinessServices:()=>{},searchResultsCount:{extensions:0,themes:0,"business-services":0},setSearchResultsCount:()=>{}});function n(e){const[t,o]=(0,r.useState)(!0),[n,s]=(0,r.useState)(""),[i,l]=(0,r.useState)([]),[m,u]=(0,r.useState)(!1),[p,d]=(0,r.useState)({extensions:0,themes:0,"business-services":0}),_=(0,r.useCallback)((e=>{d((t=>({...t,...e})))}),[]);(0,r.useEffect)((()=>{const e=(0,c.O3)("wccomHelper",{}),t=e?.installedProducts;l(t)}),[]);const w={isLoading:t,setIsLoading:o,selectedTab:n,setSelectedTab:s,isProductInstalled:function(e){return i.includes(e)},addInstalledProduct:function(e){l([...i,e])},hasBusinessServices:m,setHasBusinessServices:u,searchResultsCount:p,setSearchResultsCount:_};return(0,r.createElement)(a.Provider,{value:w},e.children)}var s=o(65736);function i(){return(0,r.createElement)("h1",{className:"woocommerce-marketplace__header-title"},(0,s.__)("Extensions","woocommerce"))}var l=o(55609),m=o(70444);const u=(0,r.createElement)(m.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(m.Path,{fillRule:"evenodd",d:"M7.25 16.437a6.5 6.5 0 1 1 9.5 0V16A2.75 2.75 0 0 0 14 13.25h-4A2.75 2.75 0 0 0 7.25 16v.437Zm1.5 1.193a6.47 6.47 0 0 0 3.25.87 6.47 6.47 0 0 0 3.25-.87V16c0-.69-.56-1.25-1.25-1.25h-4c-.69 0-1.25.56-1.25 1.25v1.63ZM4 12a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm10-2a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z",clipRule:"evenodd"}));var p=o(60247),d=o(79838);const _=(0,r.createElement)(m.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(m.Path,{d:"M15.6 7.3h-.7l1.6-3.5-.9-.4-3.9 8.5H9v1.5h2l-1.3 2.8H8.4c-2 0-3.7-1.7-3.7-3.7s1.7-3.7 3.7-3.7H10V7.3H8.4c-2.9 0-5.2 2.3-5.2 5.2 0 2.9 2.3 5.2 5.2 5.2H9l-1.4 ******* 5.7-12.5h1.4c2 0 3.7 1.7 3.7 3.7s-1.7 3.7-3.7 3.7H14v1.5h1.6c2.9 0 5.2-2.3 5.2-5.2 0-2.9-2.4-5.2-5.2-5.2z"}));function w(e){const{setIsModalOpen:t,disconnectURL:o}=e,[c,a]=(0,r.useState)(!1),n=()=>t(!1);return(0,r.createElement)(l.Modal,{title:(0,s.__)("Are you sure?","woocommerce"),onRequestClose:n,focusOnMount:!0,className:"woocommerce-marketplace__header-account-modal",style:{borderRadius:4},overlayClassName:"woocommerce-marketplace__header-account-modal-overlay"},(0,r.createElement)("p",{className:"woocommerce-marketplace__header-account-modal-text"},(0,s.__)("Keep your your account connected to manage your subscriptions, get updates and support for your extensions and themes.","woocommerce")),(0,r.createElement)(l.ButtonGroup,{className:"woocommerce-marketplace__header-account-modal-button-group"},(0,r.createElement)(l.Button,{variant:"tertiary",href:o,onClick:()=>a(!c),isBusy:c,isDestructive:!0,className:"woocommerce-marketplace__header-account-modal-button"},(0,s.__)("Disconnect account","woocommerce")),(0,r.createElement)(l.Button,{variant:"primary",onClick:n,className:"woocommerce-marketplace__header-account-modal-button"},(0,s.__)("Keep connected","woocommerce"))))}const g="discover",y="https://woocommerce.com",v="/extensions",h="/wp-json/wccom-extensions/1.0/search",E="/wp-json/wccom-extensions/1.0/categories",b=y+"/cart/",f=y+"/my-account/my-subscriptions/",k=y+"/document/managing-woocommerce-com-subscriptions/#transfer-a-woocommerce-com-subscription",N=y+"/document/managing-woocommerce-com-subscriptions/#share-a-subscription",M=c.SX+"plugins.php",C=y+"/product-download/woo-update-manager";var x=o(86989),S=o.n(x),I=o(9818);let T=function(e){return e.theme="theme",e.extension="extension",e.businessService="business-service",e}({}),A=function(e){return e.theme="theme",e.extension="extension",e.businessService="business-service",e.all="all",e}({}),L=function(e){return e.Success="success",e.Error="error",e}({});const D={notices:{}},z=(0,I.createReduxStore)("woocommerce-admin/subscription-notices",{reducer(e=D,t){switch(t.type){case"ADD_NOTICE":return{...e,notices:{...e.notices,[t.productKey]:{productKey:t.productKey,message:t.message,status:t.status,options:t.options}}};case"REMOVE_NOTICE":const o={...e.notices};return o[t.productKey]&&delete o[t.productKey],{...e,notices:o}}return e},actions:{addNotice:(e,t,o,r)=>({type:"ADD_NOTICE",productKey:e,message:t,status:o,options:r}),removeNotice:e=>({type:"REMOVE_NOTICE",productKey:e})},selectors:{notices:e=>e?Object.values(e.notices):[],getNotice(e,t){if(e)return e.notices[t]}}});(0,I.register)(z);const O=100,P=new Map;function j(){for(;P.size>O;)P.delete(P.keys().next().value)}async function U(e,t){return P.get(e)?new Promise((t=>{t(P.get(e))})):new Promise(((o,r)=>{fetch(e,{signal:t}).then((e=>{if(!e.ok)throw new Error(e.statusText);return e.json()})).then((t=>{P.set(e,t),j(),o(t)})).catch((()=>{r()}))}))}async function Z(e,t){const o=y+h+"?"+e.toString();return new Promise(((e,r)=>{U(o,t).then((t=>{const o=t.products.map((e=>{var t,o,r;return{id:e.id,slug:e.slug,title:e.title,image:e.image,type:e.type,freemium_type:e.freemium_type,description:e.excerpt,vendorName:e.vendor_name,vendorUrl:e.vendor_url,icon:e.icon,url:e.link,price:null!==(t=e.raw_price)&&void 0!==t?t:e.price,regularPrice:e.regular_price,isOnSale:e.is_on_sale,averageRating:null!==(o=e.rating)&&void 0!==o?o:null,reviewsCount:null!==(r=e.reviews_count)&&void 0!==r?r:null,isInstallable:e.is_installable,featuredImage:e.featured_image,productCategory:e.product_category,color:e.color,billingPeriod:e.billing_period,billingPeriodInterval:e.billing_period_interval,currency:e.currency}})),r=t.total_pages,c=t.total_products;e({products:o,totalPages:r,totalProducts:c})})).catch(r)}))}async function R(){let e="/wc/v3/marketplace/featured";c.MV.userLocale&&(e=`${e}?locale=${c.MV.userLocale}`);try{return await async function(e){const t=JSON.stringify(e);return P.get(t)?new Promise((e=>{e(P.get(t))})):new Promise(((o,r)=>{S()(e).then((e=>{P.set(t,e),j(),o(e)})).catch((()=>{r()}))}))}({path:e.toString()})}catch(e){return[]}}function B(e){switch(e){case"themes":return T.theme;case"business-services":return T.businessService;default:return T.extension}}function Q(e){if(!0===e.active)return Promise.resolve();const t=new URLSearchParams;return t.append("product_key",e.product_key),S()({path:"/wc/v3/marketplace/subscriptions/connect".toString(),method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:t})}function V(e,t){return new Promise(((o,r)=>{window.wp.updates?window.wp.updates.ajax(e,{...t,success:e=>{o({success:!0,data:e})},error:e=>{r({success:!1,data:{message:e.errorMessage}})}}):r((0,s.__)("Please reload and try again","woocommerce"))}))}function H(e,t){return V("install-"+e,{slug:t})}function G(e,t,o,c){o===L.Error?(0,I.dispatch)(z).addNotice(e,t,o,c):(c?.icon||(c={...c,icon:(0,r.createElement)(l.Icon,{icon:"saved"})}),(0,I.dispatch)("core/notices").createSuccessNotice(t,c))}const Y=e=>{(0,I.dispatch)(z).removeNotice(e)},F=e=>({id:e.product_id,title:e.product_name,image:"",type:e.product_type,description:"",vendorName:"",vendorUrl:"",icon:e.product_icon,url:e.product_url,price:-1,regularPrice:-1,isOnSale:!1,averageRating:null,reviewsCount:null,isInstallable:!1,currency:""}),W=(e,t)=>{if(!e)return e;const o=new URL(e);return o?(t.forEach((([e,t])=>{o.searchParams.set(e,t)})),o.toString()):e},$=e=>e.product_key?W(f,[["key",e.product_key.toString()]]):f,K=e=>W(b,[["renew_product",e.product_id.toString()],["product_key",e.product_key],["order_id",e.order_id.toString()]]),q=e=>W(b,[["add-to-cart",e.product_id.toString()]]),J=()=>{const e=(0,c.O3)("wccomHelper",{});return e.connectURL?W(e.connectURL,[["redirect_admin_url",encodeURIComponent(window.location.href)]]):""};function X(){var e,t;const[o,a]=(0,r.useState)(!1),n=()=>a(!0),i=(0,c.O3)("wccomHelper",{}),m=null!==(e=i?.isConnected)&&void 0!==e&&e,g=J(),v=i?.userEmail,h=null!==(t=i?.userAvatar)&&void 0!==t?t:u,E=y+"/my-dashboard/",b=m?E:g,f=m?(0,s.__)("Connected","woocommerce"):(0,s.__)("Not Connected","woocommerce");return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(l.DropdownMenu,{className:"woocommerce-marketplace__user-menu",icon:m?(0,r.createElement)("img",{src:h,alt:"",className:"woocommerce-marketplace__menu-avatar-image"}):u,label:(0,s.__)("User options","woocommerce")},(()=>(0,r.createElement)(r.Fragment,null,(0,r.createElement)(l.MenuGroup,{className:"woocommerce-layout__homescreen-display-options",label:f},(0,r.createElement)(l.MenuItem,{className:"woocommerce-marketplace__menu-item",href:b},m?(0,r.createElement)(r.Fragment,null,(0,r.createElement)(p.Z,{icon:u,size:24,className:"woocommerce-marketplace__menu-icon"}),(0,r.createElement)("span",{className:"woocommerce-marketplace__main-text"},v)):(0,r.createElement)(r.Fragment,null,(0,r.createElement)(p.Z,{icon:u,size:24,className:"woocommerce-marketplace__menu-icon"}),(0,r.createElement)("div",{className:"woocommerce-marketplace__menu-text"},(0,s.__)("Connect account","woocommerce"),(0,r.createElement)("span",{className:"woocommerce-marketplace__sub-text"},(0,s.__)("Manage your subscriptions, get updates and support for your extensions and themes.","woocommerce"))))),(0,r.createElement)(l.MenuItem,{href:E},(0,r.createElement)(p.Z,{icon:d.Z,size:24,className:"woocommerce-marketplace__menu-icon"}),(0,s.__)("WooCommerce.com account","woocommerce"))),m&&(0,r.createElement)(l.MenuGroup,{className:"woocommerce-layout__homescreen-display-options"},(0,r.createElement)(l.MenuItem,{onClick:n},(0,r.createElement)(p.Z,{icon:_,size:24,className:"woocommerce-marketplace__menu-icon"}),(0,s.__)("Disconnect account","woocommerce")))))),o&&(0,r.createElement)(w,{setIsModalOpen:a,disconnectURL:g}))}var ee,te=o(80225),oe=o(10431);const re=(0,c.O3)("wccomHelper",{}),ce=null!==(ee=re?.wooUpdateCount)&&void 0!==ee?ee:0,ae=(e,t)=>{const o=t.term?{term:t.term.trim()}:{};(0,oe.navigateTo)({url:(0,oe.getNewPath)({tab:e===g?void 0:e},v,o)})},ne=(e,t=!1,o)=>{if(""===e)return o;const r={...o};return t||delete r["business-services"],r},se=e=>{const{additionalClassNames:t}=e,o=(0,r.useContext)(a),{selectedTab:c,isLoading:n,setSelectedTab:i,hasBusinessServices:m}=o,{searchResultsCount:u}=o,p=(0,oe.useQuery)(),d=(0,r.useMemo)((()=>({discover:{name:"discover",title:(0,s.__)("Discover","woocommerce"),showUpdateCount:!1,updateCount:0},extensions:{name:"extensions",title:(0,s.__)("Extensions","woocommerce"),showUpdateCount:!!p.term&&!n,updateCount:u.extensions},themes:{name:"themes",title:(0,s.__)("Themes","woocommerce"),showUpdateCount:!!p.term&&!n,updateCount:u.themes},"business-services":{name:"business-services",title:(0,s.__)("Business services","woocommerce"),showUpdateCount:!!p.term&&!n,updateCount:u["business-services"]},"my-subscriptions":{name:"my-subscriptions",title:(0,s.__)("My subscriptions","woocommerce"),showUpdateCount:!0,updateCount:ce}})),[p,n,u]),[_,w]=(0,r.useState)(ne("",!1,d));return(0,r.useEffect)((()=>{p?.tab&&d[p.tab]?i(p.tab):Object.keys(p).length>0&&i(g)}),[p,i,d]),(0,r.useEffect)((()=>{w(ne(c,m,d)),"business-services"!==c||m||ae("extensions",p)}),[c,m,p,d]),(0,r.createElement)("nav",{className:(0,te.Z)("woocommerce-marketplace__tabs",t||[])},((e,t,o,c)=>{const{selectedTab:a,setSelectedTab:n}=e,s=e=>{e!==a&&(n(e),ae(e,c))},i=[];for(const e in t)i.push(o[e]?.href?(0,r.createElement)("a",{className:(0,te.Z)("woocommerce-marketplace__tab-button","components-button",`woocommerce-marketplace__tab-${e}`),href:o[e]?.href,key:e},o[e]?.title):(0,r.createElement)(l.Button,{className:(0,te.Z)("woocommerce-marketplace__tab-button",`woocommerce-marketplace__tab-${e}`,{"is-active":e===a}),onClick:()=>s(e),key:e},o[e]?.title,o[e]?.showUpdateCount&&o[e]?.updateCount>0&&(0,r.createElement)("span",{className:(0,te.Z)("woocommerce-marketplace__update-count",`woocommerce-marketplace__update-count-${e}`,{"is-active":e===a})},(0,r.createElement)("span",null," ",o[e]?.updateCount," "))));return i})(o,_,d,p))},ie=function(){const[e,t]=(0,r.useState)(""),o=(0,s.__)("Search Marketplace","woocommerce"),c=(0,oe.useQuery)();(0,r.useEffect)((()=>{c.term?t(c.term):t("")}),[c.term]);const a=t=>{const o=c;return o.tab&&"my-subscriptions"!==o.tab||(o.tab="extensions"),o.term=void 0!==t?t:e.trim(),o.term||delete o.term,(0,oe.navigateTo)({url:(0,oe.getNewPath)(o,v,{})}),[]};return(0,r.createElement)(l.SearchControl,{label:o,placeholder:o,value:e,onChange:t,onKeyUp:e=>{"Enter"===e.key&&a(),"Escape"===e.key&&t("")},onClose:()=>{t(""),a("")},className:"woocommerce-marketplace__search"})};function le(){return(0,r.createElement)("header",{className:"woocommerce-marketplace__header"},(0,r.createElement)(i,null),(0,r.createElement)(se,{additionalClassNames:["woocommerce-marketplace__header-tabs"]}),(0,r.createElement)(ie,null),(0,r.createElement)("div",{className:"woocommerce-marketplace__header-meta"},(0,r.createElement)(X,null)))}var me=o(25158),ue=o(14599),pe=o(96483),de=o(22629),_e=o(67221);const we=function(e){const{product:t}=e,{user:o,currentUserCan:c}=(0,_e.useUser)(),{selectedTab:n,isProductInstalled:i}=(0,r.useContext)(a),m={USD:"$%s",AUD:"A$%s",CAD:"C$%s",EUR:"€%s",GBP:"£%s"},u=e=>m[e]||"%s";function p(){return 0===t.price?(0,s.__)("Free download","woocommerce"):"primary"===t.freemium_type?(0,s.__)("Free plan available","woocommerce"):(0,s.sprintf)(u(t.currency),t.price)}function d(){return"primary"===t.freemium_type?"":0!==t.price?function(){if(1===t.billingPeriodInterval||""===t.billingPeriod)switch(t.billingPeriod){case"day":return(0,s.__)("daily","woocommerce");case"week":return(0,s.__)("weekly","woocommerce");case"month":return(0,s.__)("monthly","woocommerce");case"year":case"":return(0,s.__)("annually","woocommerce");default:return""}let e;switch(t.billingPeriod){case"day":e=(0,s.__)("days","woocommerce");break;case"week":e=(0,s.__)("weeks","woocommerce");break;case"month":e=(0,s.__)("months","woocommerce");break;default:e=(0,s.__)("years","woocommerce")}return(0,s.sprintf)((0,s.__)("every %1$d %2$s","woocommerce"),t.billingPeriodInterval,e)}():""}return _=t,!(o&&_&&c("install_plugins")&&_.isInstallable&&"theme"!==_.type&&"discover"!==n)||_.slug&&i(_.slug)?(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"woocommerce-marketplace__product-card__price"},(0,r.createElement)("span",{className:"woocommerce-marketplace__product-card__price-label"},(0,r.createElement)("span",{className:"screen-reader-text"},t.isOnSale?(0,s.sprintf)((0,s.__)("Sale Price %1$s %3$s, regular price %2$s %3$s","woocommerce"),p(),(0,s.sprintf)(u(t.currency),t.regularPrice),d()):0!==t.price&&"primary"!==t.freemium_type?(0,s.sprintf)((0,s.__)(" %1$s, %2$s ","woocommerce"),p(),d()):p()),(0,r.createElement)("span",{"aria-hidden":!0},p())),t.isOnSale&&(0,r.createElement)("span",{className:"woocommerce-marketplace__product-card__on-sale","aria-hidden":!0},(0,s.sprintf)(u(t.currency),t.regularPrice)),(0,r.createElement)("span",{className:"woocommerce-marketplace__product-card__price-billing","aria-hidden":!0},d())),(0,r.createElement)("div",{className:"woocommerce-marketplace__product-card__rating"},null!==t.averageRating&&(0,r.createElement)(r.Fragment,null,(0,r.createElement)("span",{className:"woocommerce-marketplace__product-card__rating-icon"},(0,r.createElement)(l.Icon,{icon:"star-filled",size:16})),(0,r.createElement)("span",{className:"woocommerce-marketplace__product-card__rating-average"},(0,r.createElement)("span",{"aria-hidden":!0},t.averageRating),(0,r.createElement)("span",{className:"screen-reader-text"},(0,s.sprintf)((0,s.__)("%.1f stars","woocommerce"),t.averageRating))),(0,r.createElement)("span",{className:"woocommerce-marketplace__product-card__rating-count"},(0,r.createElement)("span",{"aria-hidden":!0},"(",t.reviewsCount,")"),(0,r.createElement)("span",{className:"screen-reader-text"},(0,s.sprintf)((0,s.__)("from %d reviews","woocommerce"),t.reviewsCount)))))):(0,r.createElement)(r.Fragment,null,(0,r.createElement)("span",{className:"woocommerce-marketplace__product-card__add-to-store"},(0,r.createElement)(l.Button,{variant:"secondary",onClick:function(){(0,ue.recordEvent)("marketplace_add_to_store_clicked",{product_id:t.id}),(0,oe.navigateTo)({url:(0,oe.getNewPath)({installProduct:t.id})})}},(0,s.__)("Add to Store","woocommerce"))));var _},ge=function(e){var t;const{isLoading:o,type:c}=e,a=(0,oe.useQuery)(),n=null!==(t=e.product)&&void 0!==t?t:{id:null,title:"",description:"",vendorName:"",vendorUrl:"",icon:"",label:null,primary_color:null,url:"",price:0,image:"",averageRating:null,reviewsCount:null,featuredImage:"",color:"",productCategory:"",billingPeriod:"",billingPeriodInterval:0,currency:"",isOnSale:!1,regularPrice:0,type:""};function i(){return"promoted"===n.label}function m(t,o){const r=e.tracksData;r.position&&(o.position=r.position),r.label&&(o.label=r.label),r.group&&(o.group=r.group),r.group_id&&(o.group_id=r.group_id),r.searchTerm&&(o.search_term=r.searchTerm),r.category&&(o.category=r.category),(0,ue.queueRecordEvent)(t,o)}const u=c===T.theme,p=c===T.businessService;let d=n?.vendorName;n?.vendorName&&n?.vendorUrl&&(d=(0,r.createElement)("a",{href:n.vendorUrl,rel:"noopener noreferrer",onClick:()=>{m("marketplace_product_card_vendor_clicked",{product:n.title,vendor:n.vendorName,product_type:c})}},n.vendorName));const _=(0,te.Z)("woocommerce-marketplace__product-card",`woocommerce-marketplace__product-card--${c}`,{"is-loading":o,"is-small":e.small,"is-sponsored":i()}),w=()=>(0,r.createElement)("a",{className:"woocommerce-marketplace__product-card__link",href:a.ref?W(n.url,[["utm_content",a.ref]]):n.url,rel:"noopener noreferrer",onClick:()=>{m("marketplace_product_card_clicked",{product_id:n.id,product_name:n.title,vendor:n.vendorName,product_type:c})}},o?" ":n.title),g=(0,de.decodeEntities)(n.description);return(0,r.createElement)(l.Card,{className:_,id:`product-${n.id}`,tabIndex:-1,"aria-hidden":o,style:i()&&n.primary_color?{background:`linear-gradient(${n.primary_color} 0, ${n.primary_color} 5px, white 5px, white)`}:{}},p?(0,r.createElement)((()=>(0,r.createElement)("div",{className:"woocommerce-marketplace__business-card"},(0,r.createElement)("div",{className:"woocommerce-marketplace__business-card__header",style:{backgroundColor:n.color}},(0,r.createElement)("img",{src:`${n.featuredImage}?h=288`,alt:""})),(0,r.createElement)("div",{className:"woocommerce-marketplace__business-card__content"},(0,r.createElement)("div",{className:"woocommerce-marketplace__business-card__main-content"},(0,r.createElement)("h2",null,(0,r.createElement)(w,null)),(0,r.createElement)("p",{className:"woocommerce-marketplace__product-card__description"},g)),(0,r.createElement)("div",{className:"woocommerce-marketplace__business-card__badge"},(0,r.createElement)("span",null,n.productCategory))))),null):(0,r.createElement)("div",{className:"woocommerce-marketplace__product-card__content"},u&&(0,r.createElement)("div",{className:"woocommerce-marketplace__product-card__image"},!o&&(0,r.createElement)("img",{className:"woocommerce-marketplace__product-card__image-inner",src:n.image,alt:n.title})),(0,r.createElement)("div",{className:"woocommerce-marketplace__product-card__header"},(0,r.createElement)("div",{className:"woocommerce-marketplace__product-card__details"},!u&&(0,r.createElement)(r.Fragment,null,o&&(0,r.createElement)("div",{className:"woocommerce-marketplace__product-card__icon"}),!o&&n.icon&&(0,r.createElement)("img",{className:"woocommerce-marketplace__product-card__icon",src:n.icon,alt:n.title})),(0,r.createElement)("div",{className:"woocommerce-marketplace__product-card__meta"},(0,r.createElement)("h2",{className:"woocommerce-marketplace__product-card__title"},(0,r.createElement)(w,null)),o&&(0,r.createElement)("p",{className:"woocommerce-marketplace__product-card__vendor-details"},(0,r.createElement)("span",{className:"woocommerce-marketplace__product-card__vendor"})),!o&&(0,r.createElement)("p",{className:"woocommerce-marketplace__product-card__vendor-details"},d&&(0,r.createElement)("span",{className:"woocommerce-marketplace__product-card__vendor"},(0,r.createElement)("span",null,(0,s.__)("By ","woocommerce")),d),d&&i()&&(0,r.createElement)("span",{"aria-hidden":"true",className:"woocommerce-marketplace__product-card__vendor-details__separator"},"·"),i()&&(0,r.createElement)("span",{className:"woocommerce-marketplace__product-card__sponsored-label"},(0,s.__)("Sponsored","woocommerce")))))),!u&&(0,r.createElement)("p",{className:"woocommerce-marketplace__product-card__description"},!o&&g),T.businessService!==e?.product?.type&&(0,r.createElement)("footer",{className:"woocommerce-marketplace__product-card__footer"},o&&(0,r.createElement)("div",{className:"woocommerce-marketplace__product-card__price"}),!o&&e.product&&(0,r.createElement)(we,{product:e.product}))))};var ye=o(79843);function ve(e){const t=(0,c.O3)("wccomHelper",{}),o=(0,te.Z)("woocommerce-marketplace__product-list-content",e.className),[a,n]=(0,r.useState)(1),s=()=>{const e=window.innerWidth;n(e>=1920?4:e>=1024?3:e>=769?2:1)};(0,r.useEffect)((()=>(s(),window.addEventListener("resize",s),()=>window.removeEventListener("resize",s))),[]);const i=2*a-1;return(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:o},e.products.map(((o,a)=>(0,r.createElement)(r.Fragment,{key:o.id},(0,r.createElement)(ge,{key:o.id,type:e.type,product:{id:o.id,slug:o.slug,title:o.title,image:o.image,type:o.type,freemium_type:o.freemium_type,icon:o.icon,label:o.label,primary_color:o.primary_color,vendorName:o.vendorName,vendorUrl:o.vendorUrl?W(o.vendorUrl,[["utm_source","extensionsscreen"],["utm_medium","product"],["utm_campaign","wcaddons"],["utm_content","devpartner"]]):"",price:o.price,url:W(o.url,Object.entries({...t.inAppPurchaseURLParams,...void 0!==e.productGroup?{utm_group:e.productGroup}:{}})),averageRating:o.averageRating,reviewsCount:o.reviewsCount,description:o.description,isInstallable:o.isInstallable,color:o.color,featuredImage:o.featuredImage,productCategory:o.productCategory,billingPeriod:o.billingPeriod,billingPeriodInterval:o.billingPeriodInterval,currency:o.currency,isOnSale:o.isOnSale,regularPrice:o.regularPrice},tracksData:{position:a+1,...o.label&&{label:o.label},...e.productGroup&&{group_id:e.productGroup},...e.group&&{group:e.group},...e.searchTerm&&{searchTerm:e.searchTerm},...e.category&&{category:e.category}}}),a===i&&"theme"===e.type&&(0,r.createElement)(ye.$p,{redirectToCYSFlow:()=>{const e=(0,pe.addQueryArgs)(`${c.SX}admin.php`,{page:"wc-admin",path:"/customize-store/design"});window.location.href=e}}))))))}var he=o(29688),Ee=o(45842),be=o(86020);function fe(e){const{title:t,groupURL:o}=e,c=""===t,a=(0,te.Z)("woocommerce-marketplace__product-list-header",{"is-loading":c});return(0,r.createElement)("div",{className:a,"aria-hidden":c},(0,r.createElement)("h2",{className:"woocommerce-marketplace__product-list-title"},t),null!==o&&(0,r.createElement)("span",{className:"woocommerce-marketplace__product-list-link"},(0,r.createElement)(be.Link,{href:o,target:"_blank",onClick:()=>{(0,ue.recordEvent)("marketplace_see_more_clicked",{group_title:t,group_url:o})}},(0,s.__)("See more","woocommerce"),(0,r.createElement)(p.Z,{icon:(0,s.isRTL)()?he.Z:Ee.Z}))))}function ke(e){const{title:t,products:o,groupURL:c,type:a,productGroup:n}=e;return(0,r.createElement)("div",{className:"woocommerce-marketplace__product-list"},(0,r.createElement)(fe,{title:t,groupURL:c}),(0,r.createElement)(ve,{group:t,products:o,type:a,productGroup:n}))}function Ne(e){const{hasTitle:t,type:o}=e,c=e.placeholderCount||12;return(0,r.createElement)("div",{className:"woocommerce-marketplace__product-list"},!1!==t&&(0,r.createElement)(fe,{title:"",groupURL:null}),(0,r.createElement)("div",{className:"woocommerce-marketplace__product-list-content"},[...Array(c)].map(((e,t)=>(0,r.createElement)(ge,{key:t,isLoading:!0,type:o,tracksData:{}})))))}function Me(e){if(""===e.category)return;const t=e.view||"discover",o=e.search_term||null,r=e.product_type||null,c=e.category||null,a={...t&&{view:t},...o&&{search_term:o},...r&&{product_type:r},...c&&{category:c}};t&&["extensions","themes","business-services"].includes(t)&&!c&&(a.category="_all"),(0,ue.recordEvent)("marketplace_view",a)}function Ce(){const[e,t]=(0,r.useState)([]),o=(0,r.useContext)(a),{isLoading:c,setIsLoading:n}=o;if((0,r.useEffect)((()=>{n(!0),R().then((e=>Array.isArray(e)?e:[])).then((e=>{t(e),function(e){const t=e.flatMap((e=>e.items)).map((e=>e.id));(0,ue.recordEvent)("marketplace_discover_viewed",{view:"discover",product_ids:t}),Me({view:"discover"})}(e)})).finally((()=>{n(!1)}))}),[]),c)return(0,r.createElement)("div",{className:"woocommerce-marketplace__discover"},(0,r.createElement)(Ne,{placeholderCount:9,type:T.extension}));const s=e.flatMap((e=>e));return(0,r.createElement)("div",{className:"woocommerce-marketplace__discover"},s.map((e=>(0,r.createElement)(ke,{key:e.id,productGroup:e.id,title:e.title,products:e.items,groupURL:e.url,type:e.itemType}))))}var xe=o(29346),Se=o(94333);function Ie(e){const t=""===e.label,o=(0,te.Z)("woocommerce-marketplace__category-item-button",{"woocommerce-marketplace__category-item-button--selected":e.selected,"is-loading":t});return(0,r.createElement)("button",{className:o,onClick:function(e){const t=e.currentTarget.value;t&&(0,oe.navigateTo)({url:(0,oe.getNewPath)({category:t})})},value:e.slug,"aria-hidden":t},e.label)}var Te=o(70902),Ae=o(13608);function Le(e){function t(t){const o=t.currentTarget.value;o&&(e.onClick(),(0,oe.navigateTo)({url:(0,oe.getNewPath)({category:o})}))}return(0,r.createElement)("ul",{className:"woocommerce-marketplace__category-dropdown-list"},e.categories.map((o=>(0,r.createElement)("li",{className:"woocommerce-marketplace__category-dropdown-item",key:o.slug},(0,r.createElement)("button",{className:(0,te.Z)("woocommerce-marketplace__category-dropdown-item-button",{"woocommerce-marketplace__category-dropdown-item-button--selected":o.slug===e.selected?.slug}),value:o.slug,onClick:t},o.label)))))}function De(e){return(0,r.createElement)(l.Dropdown,{renderToggle:({isOpen:t,onToggle:o})=>(0,r.createElement)("button",{onClick:()=>{t||(0,ue.recordEvent)("marketplace_category_dropdown_opened",{type:e.type}),o()},className:e.buttonClassName,"aria-label":(0,s.__)("Toggle category dropdown","woocommerce")},e.label,(0,r.createElement)(p.Z,{icon:t?Te.Z:Ae.Z,size:e.arrowIconSize})),className:e.className,renderContent:({onToggle:t})=>(0,r.createElement)(Le,{categories:e.categories,selected:e.selected,onClick:t}),contentClassName:e.contentClassName})}const ze={[T.extension]:"_all",[T.theme]:"themes",[T.businessService]:"business-services"};function Oe(e){const[t,o]=(0,r.useState)(),[a,n]=(0,r.useState)(!1),[i,m]=(0,r.useState)([]),[u,p]=(0,r.useState)(!1),[d,_]=(0,r.useState)("start"),w=(0,r.useRef)(null),g=(0,r.useRef)(null),v=(0,oe.useQuery)();function h(){if(w.current&&w.current.parentElement?.scrollWidth){const e=w.current.scrollWidth>w.current.parentElement.scrollWidth;p(e)}}(0,r.useEffect)((()=>{n(!0),function(e){const t=new URL(y+E);return c.MV.userLocale&&t.searchParams.set("locale",c.MV.userLocale),e===T.theme?t.searchParams.set("parent","themes"):e===T.businessService&&t.searchParams.set("parent","business-services"),U(t.toString()).then((e=>e)).catch((()=>[]))}(e.type).then((e=>{const t=e.map((e=>({...e,selected:!1}))).filter((e=>"_featured"!==e.slug));m(t)})).catch((()=>{m([])})).finally((()=>{n(!1)}))}),[e.type,m]),(0,r.useEffect)((()=>{let t=ze[e.type];v.category&&(t=v.category);const r=i.find((e=>e.slug===t));r&&o(r)}),[v.category,e.type,i]),(0,r.useEffect)((()=>{g.current&&g.current.scrollIntoView({block:"nearest",inline:"center"})}),[t]);const b=(0,Se.useDebounce)(h,300),f=(0,Se.useDebounce)((function(){const e=w.current;if(!e)return;const{scrollLeft:t,scrollWidth:o,clientWidth:r}=e;t<10?_("start"):t+r<o?_("middle"):t+r===o&&_("end")}),100);function k(e){w.current&&w.current.scrollTo({left:w.current.scrollLeft+e,behavior:"smooth"})}return(0,r.useEffect)((()=>{window.addEventListener("resize",b);const e=w.current;return e&&e.addEventListener("scroll",f),()=>{window.removeEventListener("resize",b),e&&e.removeEventListener("scroll",f)}}),[b,f]),(0,r.useEffect)((()=>{h()}),[i]),a?(0,r.createElement)(r.Fragment,null,(0,r.createElement)("ul",{className:"woocommerce-marketplace__category-selector"},[...Array(5)].map(((e,t)=>(0,r.createElement)("li",{key:t,className:"woocommerce-marketplace__category-item"},(0,r.createElement)(Ie,{slug:"",label:"",selected:!1})))))):(0,r.createElement)(r.Fragment,null,(0,r.createElement)("ul",{className:"woocommerce-marketplace__category-selector","aria-label":"Categories",ref:w},i.map((e=>(0,r.createElement)("li",{className:"woocommerce-marketplace__category-item",key:e.slug,ref:e.slug===t?.slug?g:null},(0,r.createElement)(Ie,(0,xe.Z)({},e,{selected:e.slug===t?.slug,"aria-current":e.slug===t?.slug})))))),(0,r.createElement)("div",{className:"woocommerce-marketplace__category-selector--full-width"},(0,r.createElement)(De,{type:e.type,label:function(){const e=(0,s.__)("All Categories","woocommerce");return t?"All"===t.label?e:t.label:e}(),categories:i,buttonClassName:"woocommerce-marketplace__category-dropdown-button",className:"woocommerce-marketplace__category-dropdown",contentClassName:"woocommerce-marketplace__category-dropdown-content",selected:t})),u&&(0,r.createElement)(r.Fragment,null,(0,r.createElement)("button",{onClick:function(){k(-200)},className:"woocommerce-marketplace__category-navigation-button woocommerce-marketplace__category-navigation-button--prev",hidden:"start"===d,"aria-label":"Scroll to previous categories",tabIndex:-1},(0,r.createElement)(l.Icon,{icon:"arrow-left-alt2"})),(0,r.createElement)("button",{onClick:function(){k(200)},className:"woocommerce-marketplace__category-navigation-button woocommerce-marketplace__category-navigation-button--next",hidden:"end"===d,"aria-label":"Scroll to next categories",tabIndex:-1},(0,r.createElement)(l.Icon,{icon:"arrow-right-alt2"}))))}function Pe(e){const[t,o]=(0,r.useState)(),[c,a]=(0,r.useState)(!1),n={[A.all]:["most-popular","popular-themes","business-services"],[A.theme]:["popular-themes"],[A.extension]:["most-popular"],[A.businessService]:["business-services"]};return(0,r.useEffect)((()=>{a(!0),R().then((t=>{const r=n[e.type];if(!r)return;const c=t.filter((e=>r.includes(e.id)));c&&(c.forEach((e=>{e.items=e.items.slice(0,4)})),o(c))})).catch((()=>{o(void 0)})).finally((()=>{a(!1)}))}),[]),(0,r.createElement)("div",{className:"woocommerce-marketplace__no-results"},function(){if(e.type===A.all)return(0,r.createElement)(r.Fragment,null);let t=T.extension;return e.type===A.theme&&(t=T.theme),e.type===A.businessService&&(t=T.businessService),(0,r.createElement)(Oe,{type:t})}(),(0,r.createElement)("div",{className:"woocommerce-marketplace__no-results__content"},(0,r.createElement)("h2",{className:"woocommerce-marketplace__no-results__heading"},e.showHeading?e.heading:""),(0,r.createElement)("p",{className:"woocommerce-marketplace__no-results__description"},(0,s.__)("Try searching again using a different term, or take a look at our recommendations below.","woocommerce"))),(0,r.createElement)("div",{className:"woocommerce-marketplace__no-results__product-groups"},c?(0,r.createElement)(r.Fragment,null,(0,r.createElement)(Ne,{type:T.extension,placeholderCount:4}),(0,r.createElement)(Ne,{type:T.theme,placeholderCount:4}),(0,r.createElement)(Ne,{type:T.businessService,placeholderCount:4})):t&&0!==t.length?(0,r.createElement)(r.Fragment,null,t.map((e=>{return(0,r.createElement)(ke,{title:(t=e.id,"popular-themes"===t?(0,s.__)("Our favorite themes","woocommerce"):"business-services"===t?(0,s.__)("Services to help your business grow","woocommerce"):(0,s.__)("Most popular extensions","woocommerce")),products:e.items,groupURL:e.url,productGroup:e.id,type:e.itemType,key:e.id});var t}))):(0,r.createElement)(r.Fragment,null)))}var je=o(86435);const Ue={[T.extension]:{label:(0,s.__)("extensions","woocommerce"),singularLabel:(0,s.__)("extension","woocommerce")},[T.theme]:{label:(0,s.__)("themes","woocommerce"),singularLabel:(0,s.__)("theme","woocommerce")},[T.businessService]:{label:(0,s.__)("business services","woocommerce"),singularLabel:(0,s.__)("business service","woocommerce")}};function Ze(e){var t,o;const n=(0,r.useContext)(a),{isLoading:i}=n,m=Ue[e.type].label,u=(0,oe.useQuery)(),p=u?.category,d=(0,I.useSelect)((e=>e("core").getCurrentTheme()),[]),_="twentytwentyfour"===d?.stylesheet,[w,g]=(0,r.useState)(!1),y=(0,pe.addQueryArgs)(`${c.SX}admin.php`,{page:"wc-admin",path:"/customize-store/design"}),v=(0,pe.addQueryArgs)(`${c.SX}admin.php`,{page:"wc-admin",path:"/customize-store/assembler-hub"}),h=(0,I.useSelect)((e=>e(_e.ONBOARDING_STORE_NAME).getTask("customize-store")),[]),E=null!==(t=e.showAllButton)&&void 0!==t&&t,b=null!==(o=e.products)&&void 0!==o?o:[],f="business services"===m?"business-services":m,k="woocommerce-marketplace__search-",N=(0,te.Z)(k+f),M=(0,te.Z)("woocommerce-marketplace__view-all-button",k+"button-"+f);if(i)return(0,r.createElement)(r.Fragment,null,e.categorySelector&&(0,r.createElement)(Oe,{type:e.type}),(0,r.createElement)(Ne,{hasTitle:!1,type:e.type}));if(0===b.length){let t=A.all;switch(e.type){case T.extension:t=A.extension;break;case T.theme:t=A.theme;break;case T.businessService:t=A.businessService}return(0,r.createElement)(Pe,{type:t,showHeading:!1})}const C=(0,te.Z)(E?"woocommerce-marketplace__product-list-content--collapsed":"");return(0,r.createElement)("div",{className:N},(0,r.createElement)("nav",{className:"woocommerce-marketplace__sub-header"},(0,r.createElement)("div",{className:"woocommerce-marketplace__sub-header__categories"},e.categorySelector&&(0,r.createElement)(Oe,{type:e.type})),"theme"===e.type&&(0,r.createElement)(l.Button,{className:"woocommerce-marketplace__customize-your-store-button",variant:"secondary",text:(0,s.__)("Design your own","woocommerce"),onClick:()=>{_?window.location.href=h?.isComplete?v:y:g(!0)}})),w&&(0,r.createElement)(je.Nh,{setIsModalOpen:g,redirectToCYSFlow:()=>{window.location.href=y}}),(0,r.createElement)(ve,{products:b,type:e.type,className:C,searchTerm:e.searchTerm,category:p}),"theme"===e.type&&(0,r.createElement)("div",{className:"woocommerce-marketplace__browse-wp-theme-directory"},(0,r.createElement)("b",null,(0,s.__)("Didn’t find a theme you like?","woocommerce")),(0,r.createInterpolateElement)((0,s.__)(" Browse the <a>WordPress.org theme directory</a> to discover more.","woocommerce"),{a:(0,r.createElement)("a",{href:c.SX+"theme-install.php?search=e-commerce"})})),E&&(0,r.createElement)(l.Button,{className:M,variant:"secondary",text:(0,s.__)("View all","woocommerce"),onClick:()=>{return t=e.type,void(0,oe.navigateTo)({url:(0,oe.getNewPath)({section:t})});var t}}))}const Re=(0,r.createContext)({subscriptions:[],setSubscriptions:()=>{},loadSubscriptions:()=>new Promise((()=>{})),refreshSubscriptions:()=>new Promise((()=>{})),isLoading:!0,setIsLoading:()=>{}});function Be(e){const[t,o]=(0,r.useState)([]),[c,a]=(0,r.useState)(!0),n=e=>(!0===e&&a(!0),async function(){return await S()({path:"/wc/v3/marketplace/subscriptions".toString()})}().then((e=>{o(e)})).finally((()=>{e&&a(!1)}))),i=e=>(e&&a(!0),async function(){return await S()({path:"/wc/v3/marketplace/refresh".toString(),method:"POST"})}().then((e=>{o(e)})).finally((()=>{e&&a(!1)})));(0,r.useEffect)((()=>{new URLSearchParams(window.location.search).get("install")?i(!0).catch((e=>{G("woocommerce-marketplace-refresh-subscriptions",(0,s.sprintf)((0,s.__)("Error refreshing subscriptions: %s","woocommerce"),e.message),L.Error)})):n(!0).catch((e=>{G("woocommerce-marketplace-load-subscriptions",(0,s.sprintf)((0,s.__)("Error loading subscriptions: %s","woocommerce"),e.message),L.Error)}))}),[]);const l={subscriptions:t,setSubscriptions:o,loadSubscriptions:n,refreshSubscriptions:i,isLoading:c,setIsLoading:a};return(0,r.createElement)(Re.Provider,{value:l},e.children)}const Qe=[{key:"name",label:(0,s.__)("Name","woocommerce")},{key:"expiry",label:(0,s.__)("Expires/Renews on","woocommerce")},{key:"subscription",label:(0,s.__)("Subscription","woocommerce")},{key:"version",label:(0,s.__)("Version","woocommerce")}];function Ve(e){if(e.isLoading)return(0,r.createElement)(be.TablePlaceholder,{caption:(0,s.__)("Loading your subscriptions","woocommerce"),headers:e.headers});const t=e.headers.map((e=>({...e,cellClassName:"woocommerce-marketplace__my-subscriptions__table__header--"+e.key})));return(0,r.createElement)(be.Table,{className:"woocommerce-marketplace__my-subscriptions__table",headers:t,rows:e.rows})}function He(e){const t=[...Qe,{key:"actions",label:(0,s.__)("Actions","woocommerce")}];if(!(e.isLoading||e.rows&&0!==e.rows.length)){const e=(0,oe.getNewPath)({},v,{}),t=(0,r.createInterpolateElement)((0,s.__)("No extensions or themes installed. <a>Browse the Marketplace</a>","woocommerce"),{a:(0,r.createElement)("a",{href:e})});return(0,r.createElement)(be.EmptyTable,{numberOfRows:4},t)}return(0,r.createElement)(Ve,{rows:e.rows,isLoading:e.isLoading,headers:t})}function Ge(e){const t=[...Qe,{key:"actions",label:(0,s.__)("Actions","woocommerce")}];return(0,r.createElement)(Ve,{rows:e.rows,isLoading:e.isLoading,headers:t})}var Ye=o(69771),Fe=o(40886);let We=function(e){return e.Warning="warning",e.Error="error",e.Info="info",e}({});function $e(e){var t;const[o,c]=(0,r.useState)(!1),{loadSubscriptions:a}=(0,r.useContext)(Re),n=()=>{(0,ue.recordEvent)("marketplace_product_connect_button_clicked",{product_zip_slug:e.subscription.zip_slug,product_id:e.subscription.product_id}),c(!0),Y(e.subscription.product_key),Q(e.subscription).then((()=>{a(!1).then((()=>{G(e.subscription.product_key,(0,s.sprintf)((0,s.__)("%s successfully connected.","woocommerce"),e.subscription.product_name),L.Success),c(!1),e.onClose&&e.onClose()}))})).catch((()=>{G(e.subscription.product_key,(0,s.sprintf)((0,s.__)("%s couldn’t be connected.","woocommerce"),e.subscription.product_name),L.Error,{actions:[{label:(0,s.__)("Try again","woocommerce"),onClick:n}]}),c(!1),e.onClose&&e.onClose()}))};return(0,r.createElement)(l.Button,{onClick:n,variant:null!==(t=e.variant)&&void 0!==t?t:"secondary",isBusy:o,disabled:o},(0,s.__)("Connect","woocommerce"))}const Ke={installingProducts:[]},qe=(0,I.createReduxStore)("woocommerce-admin/installing",{reducer(e=Ke,t){switch(t.type){case"START_INSTALLING":return{...e,installingProducts:[...e.installingProducts,t.productKey]};case"STOP_INSTALLING":return{...e,installingProducts:[...e.installingProducts.filter((e=>e!==t.productKey))]}}return e},actions:{startInstalling:e=>({type:"START_INSTALLING",productKey:e}),stopInstalling:e=>({type:"STOP_INSTALLING",productKey:e})},selectors:{isInstalling:(e,t)=>!!e&&e.installingProducts.includes(t)}});function Je(e){var t;const{loadSubscriptions:o}=(0,r.useContext)(Re),c=(0,I.useSelect)((t=>t(qe).isInstalling(e.subscription.product_key)),[e.subscription.product_key]),a=()=>{(0,I.dispatch)(qe).stopInstalling(e.subscription.product_key)},n=t=>{o(!1).then((()=>{let o=(0,s.sprintf)((0,s.__)("%s couldn’t be installed.","woocommerce"),e.subscription.product_name);!1===t?.success&&t?.data.message&&(o+=" "+t.data.message),G(e.subscription.product_key,o,L.Error,{actions:[{label:(0,s.__)("Download and install manually","woocommerce"),url:"https://woocommerce.com/my-account/downloads/"}]}),a(),e.onError&&e.onError()})),(0,ue.recordEvent)("marketplace_product_install_failed",{product_zip_slug:e.subscription.zip_slug,product_id:e.subscription.product_id,product_current_version:e.subscription.version,error_message:t?.data?.message})};return(0,r.createElement)(l.Button,{variant:null!==(t=e.variant)&&void 0!==t?t:"link",isBusy:c,disabled:c,onClick:()=>{var t;(0,ue.recordEvent)("marketplace_product_install_button_clicked",{product_zip_slug:e.subscription.zip_slug,product_id:e.subscription.product_id,product_current_version:e.subscription.version}),(0,I.dispatch)(qe).startInstalling(e.subscription.product_key),Y(e.subscription.product_key),e.subscription.is_installable?function(e){return Q(e).then((()=>H(e.product_type,e.zip_slug).then((()=>function(e){if(!0===e.local.active)return Promise.resolve();const t=new URLSearchParams;return t.append("product_key",e.product_key),S()({path:"/wc/v3/marketplace/subscriptions/activate".toString(),method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:t}).then((()=>Promise.resolve())).catch((()=>Promise.reject({success:!1,data:{message:(0,s.sprintf)((0,s.__)("%s could not be activated. Please activate it manually.","woocommerce"),e.product_name)}})))}(e))).catch((t=>function(e){if(!1===e.active)return Promise.resolve();const t=new URLSearchParams;return t.append("product_key",e.product_key),S()({path:"/wc/v3/marketplace/subscriptions/disconnect".toString(),method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:t})}(e).finally((()=>Promise.reject(t)))))))}(e.subscription).then((()=>{o(!1).then((()=>{G(e.subscription.product_key,(0,s.sprintf)((0,s.__)("%s successfully installed.","woocommerce"),e.subscription.product_name),L.Success),a()})),(0,ue.recordEvent)("marketplace_product_installed",{product_zip_slug:e.subscription.zip_slug,product_id:e.subscription.product_id,product_current_version:e.subscription.version}),e.onSuccess&&e.onSuccess()})).catch(n):(t=e.subscription,S()({path:"/wc/v3/marketplace/subscriptions/install-url?product_key="+t.product_key}).then((e=>e?.data.url))).then((t=>{if((0,ue.recordEvent)("marketplace_product_install_url",{product_zip_slug:e.subscription.zip_slug,product_id:e.subscription.product_id,product_current_version:e.subscription.version,product_install_url:t}),a(),!t)throw new Error;window.open(t,"_self")})).catch(n)}},(0,s.__)("Install","woocommerce"))}function Xe(e){var t;return(0,r.createElement)(l.Button,{href:K(e.subscription),variant:null!==(t=e.variant)&&void 0!==t?t:"secondary",onClick:function(){(0,ue.queueRecordEvent)("marketplace_renew_button_clicked",{product_zip_slug:e.subscription.zip_slug,product_id:e.subscription.product_id})}},(0,s.__)("Renew","woocommerce"))}function et(e){var t;return(0,r.createElement)(l.Button,{href:$(e.subscription),variant:null!==(t=e.variant)&&void 0!==t?t:"secondary",onClick:function(){(0,ue.queueRecordEvent)("marketplace_auto_renew_button_clicked",{order_id:e.subscription.order_id,product_id:e.subscription.product_id})}},(0,s.__)("Renew","woocommerce"))}function tt(e){var t;return(0,r.createElement)(l.Button,{href:q(e.subscription),variant:null!==(t=e.variant)&&void 0!==t?t:"secondary",onClick:function(){(0,ue.queueRecordEvent)("marketplace_subscribe_button_clicked",{product_zip_slug:e.subscription.zip_slug,product_id:e.subscription.product_id})}},(0,s.__)("Subscribe","woocommerce"))}function ot(e){return(0,r.createElement)(l.Modal,{title:(0,s.__)("Connect to update","woocommerce"),onRequestClose:e.onClose,focusOnMount:!0,className:"woocommerce-marketplace__header-account-modal",style:{borderRadius:4},overlayClassName:"woocommerce-marketplace__header-account-modal-overlay"},(0,r.createElement)("p",{className:"woocommerce-marketplace__header-account-modal-text"},(0,s.sprintf)((0,s.__)("Version %s is available. To enable this update you need to connect your subscription to this store.","woocommerce"),e.subscription.version)),(0,r.createElement)(l.ButtonGroup,{className:"woocommerce-marketplace__header-account-modal-button-group"},(0,r.createElement)(l.Button,{variant:"tertiary",onClick:e.onClose,className:"woocommerce-marketplace__header-account-modal-button"},(0,s.__)("Cancel","woocommerce")),(0,r.createElement)($e,{subscription:e.subscription,onClose:e.onClose,variant:"primary"})))}function rt(e){return(0,r.createElement)(l.Modal,{title:(0,s.__)("Renew to update","woocommerce"),onRequestClose:e.onClose,focusOnMount:!0,className:"woocommerce-marketplace__header-account-modal",style:{borderRadius:4},overlayClassName:"woocommerce-marketplace__header-account-modal-overlay"},(0,r.createElement)("p",{className:"woocommerce-marketplace__header-account-modal-text"},(0,s.sprintf)((0,s.__)("Version %s is available. To enable this update you need to renew your subscription.","woocommerce"),e.subscription.version)),(0,r.createElement)(l.ButtonGroup,{className:"woocommerce-marketplace__header-account-modal-button-group"},(0,r.createElement)(l.Button,{variant:"tertiary",onClick:e.onClose,className:"woocommerce-marketplace__header-account-modal-button"},(0,s.__)("Cancel","woocommerce")),(0,r.createElement)(Xe,{subscription:e.subscription,variant:"primary"})))}function ct(e){return(0,r.createElement)(l.Modal,{title:(0,s.__)("Subscribe to update","woocommerce"),onRequestClose:e.onClose,focusOnMount:!0,className:"woocommerce-marketplace__header-account-modal",style:{borderRadius:4},overlayClassName:"woocommerce-marketplace__header-account-modal-overlay"},(0,r.createElement)("p",{className:"woocommerce-marketplace__header-account-modal-text"},(0,s.sprintf)((0,s.__)("Version %s is available. To enable this update you need to purchase a subscription.","woocommerce"),e.subscription.version)),(0,r.createElement)(l.ButtonGroup,{className:"woocommerce-marketplace__header-account-modal-button-group"},(0,r.createElement)(l.Button,{variant:"tertiary",onClick:e.onClose,className:"woocommerce-marketplace__header-account-modal-button"},(0,s.__)("Cancel","woocommerce")),(0,r.createElement)(tt,{subscription:e.subscription,variant:"primary"})))}(0,I.register)(qe);var at=o(42058);function nt(e){const t=(0,c.O3)("wccomHelper",{});return t?.wooUpdateManagerInstalled?t?.wooUpdateManagerActive?null:(0,r.createElement)(l.Modal,{title:(0,s.__)("Access your updates","woocommerce"),onRequestClose:e.onClose,focusOnMount:!0,className:"woocommerce-marketplace__header-account-modal",style:{borderRadius:4},overlayClassName:"woocommerce-marketplace__header-account-modal-overlay"},(0,r.createElement)("p",{className:"woocommerce-marketplace__header-account-modal-text"},(0,r.createElement)("span",{dangerouslySetInnerHTML:(0,at.ZP)((0,s.sprintf)((0,s.__)("Version %s is available. To access this update, please <b>activate the WooCommerce.com Update Manager</b> extension.","woocommerce"),e.subscription.version))})),(0,r.createElement)(l.ButtonGroup,{className:"woocommerce-marketplace__header-account-modal-button-group"},(0,r.createElement)(l.Button,{onClick:e.onClose,variant:"link"},(0,s.__)("Cancel","woocommerce")),(0,r.createElement)(l.Button,{href:M,variant:"primary"},(0,s.__)("Activate","woocommerce")))):(0,r.createElement)(l.Modal,{title:(0,s.__)("Access your updates","woocommerce"),onRequestClose:e.onClose,focusOnMount:!0,className:"woocommerce-marketplace__header-account-modal",style:{borderRadius:4},overlayClassName:"woocommerce-marketplace__header-account-modal-overlay"},(0,r.createElement)("p",{className:"woocommerce-marketplace__header-account-modal-text"},(0,r.createElement)("span",{dangerouslySetInnerHTML:(0,at.ZP)((0,s.sprintf)((0,s.__)("Version %s is available. To access this update, please first <b>install the WooCommerce.com Update Manager</b> extension. Alternatively, you can download and install it manually.","woocommerce"),e.subscription.version))})),(0,r.createElement)(l.ButtonGroup,{className:"woocommerce-marketplace__header-account-modal-button-group"},(0,r.createElement)(l.Button,{href:C,variant:"secondary"},(0,s.__)("Download","woocommerce")),(0,r.createElement)(l.Button,{href:t?.wooUpdateManagerInstallUrl,variant:"primary"},(0,s.__)("Install","woocommerce"))))}function st(e){const[t,o]=(0,r.useState)(!1),[c,a]=(0,r.useState)(!1),{loadSubscriptions:n}=(0,r.useContext)(Re),i=e.subscription.active&&e.subscription.local&&e.subscription.local.slug&&e.subscription.local.path&&e.wooUpdateManagerActive;return(0,r.createElement)(r.Fragment,null,t?""===e.subscription.product_key?(0,r.createElement)(ct,{onClose:()=>o(!1),subscription:e.subscription}):e.subscription.expired?(0,r.createElement)(rt,{subscription:e.subscription,onClose:()=>o(!1)}):e.subscription.active?e.wooUpdateManagerActive?null:(0,r.createElement)(nt,{subscription:e.subscription,onClose:()=>o(!1)}):(0,r.createElement)(ot,{subscription:e.subscription,onClose:()=>o(!1)}):null,(0,r.createElement)(l.Button,{variant:"link",className:"woocommerce-marketplace__my-subscriptions-update",onClick:function t(){var r;(0,ue.recordEvent)("marketplace_product_update_button_clicked",{product_zip_slug:e.subscription.zip_slug,product_id:e.subscription.product_id,product_installed_version:e.subscription.local.installed,product_current_version:e.subscription.version}),i?(Y(e.subscription.product_key),window.wp.updates?(a(!0),(r=e.subscription,V("update-"+r.product_type,{slug:r.local.slug,[r.product_type]:r.local.path})).then((()=>{n(!1).then((()=>{G(e.subscription.product_key,(0,s.sprintf)((0,s.__)("%s updated successfully.","woocommerce"),e.subscription.product_name),L.Success),a(!1)})),(0,ue.recordEvent)("marketplace_product_updated",{product_zip_slug:e.subscription.zip_slug,product_id:e.subscription.product_id,product_installed_version:e.subscription.local.installed,product_current_version:e.subscription.version})})).catch((()=>{G(e.subscription.product_key,(0,s.sprintf)((0,s.__)("%s couldn’t be updated.","woocommerce"),e.subscription.product_name),L.Error,{actions:[{label:(0,s.__)("Try again","woocommerce"),onClick:t}]}),a(!1),(0,ue.recordEvent)("marketplace_product_update_failed",{product_zip_slug:e.subscription.zip_slug,product_id:e.subscription.product_id,product_installed_version:e.subscription.local.installed,product_current_version:e.subscription.version})}))):G(e.subscription.product_key,(0,s.sprintf)((0,s.__)("%s couldn’t be updated.","woocommerce"),e.subscription.product_name),L.Error,{actions:[{label:(0,s.__)("Reload page and try again","woocommerce"),onClick:()=>{window.location.reload()}}]})):o(!0)},isBusy:c,disabled:c,label:(0,s.sprintf)((0,s.__)("Update to %s","woocommerce"),e.subscription.version),showTooltip:!0,tooltipPosition:"top center"},c?(0,s.__)("Updating","woocommerce"):(0,s.__)("Update","woocommerce")))}function it(e){const[t,o]=(0,r.useState)(!1),[c,a]=(0,r.useState)(!1),n=(0,r.useRef)(null);(0,r.useEffect)((()=>()=>{n.current&&clearTimeout(n.current)}),[]);const s=()=>{e.explanationOnHover&&(n.current&&clearTimeout(n.current),o(!0))},i=()=>{e.explanationOnHover&&(n.current&&clearTimeout(n.current),n.current=setTimeout((()=>{o(!1)}),350))};return(0,r.createElement)("button",{onClick:()=>a(!c),onMouseOver:s,onFocus:s,onMouseOut:i,onBlur:i,className:(0,te.Z)("woocommerce-marketplace__my-subscriptions__product-status",`woocommerce-marketplace__my-subscriptions__product-status--${e.level}`)},e.text,""!==e.explanation&&(c||e.explanationOnHover&&t)&&(0,r.createElement)(l.Popover,{className:"woocommerce-marketplace__my-subscriptions__popover",position:"top center",focusOnMount:!1,onMouseOver:s,onMouseOut:i,onFocus:s,onBlur:i},e.explanation))}var lt=o(77843);function mt(e){const t=[{title:(0,s.__)("Manage in Plugins","woocommerce"),icon:(0,r.createElement)(r.Fragment,null),onClick:()=>{window.location.href=c.SX+"plugins.php"}}];return e.subscription.is_shared||t.unshift({title:(0,s.__)("Manage on WooCommerce.com","woocommerce"),icon:(0,r.createElement)(r.Fragment,null),onClick:()=>{window.open("https://woocommerce.com/my-account/my-subscriptions","_blank")}}),e.subscription.documentation_url&&t.unshift({title:(0,s.__)("View documentation","woocommerce"),icon:(0,r.createElement)(r.Fragment,null),onClick:()=>{window.open(e.subscription.documentation_url,"_blank")}}),(0,r.createElement)(l.DropdownMenu,{icon:lt.Z,label:(0,s.__)("Actions","woocommerce"),controls:t})}function ut(e){return(0,r.createElement)("span",{className:"woocommerce-marketplace__my-subscriptions-version"},e.span)}function pt(e,t){const o=(0,c.O3)("wccomHelper",{});return e.local.version===e.version?(0,r.createElement)(ut,{span:e.local.version}):e.local.version&&e.version&&"installed"===t?(0,r.createElement)(st,{subscription:e,wooUpdateManagerActive:o?.wooUpdateManagerActive}):e.version?(0,r.createElement)(ut,{span:e.version}):e.local.version?(0,r.createElement)(ut,{span:e.local.version}):""}function dt(e){return W(e,[["utm_source","subscriptionsscreen"],["utm_medium","product"],["utm_campaign","wcaddons"],["utm_content","product-name"]])}function _t(e){let t=(0,r.createElement)(p.Z,{icon:Fe.Z,size:40});return e.product_icon&&(t=(0,r.createElement)("img",{src:e.product_icon,alt:(0,s.sprintf)((0,s.__)("%s icon","woocommerce"),e.product_name)})),{display:(0,r.createElement)("div",{className:"woocommerce-marketplace__my-subscriptions__product"},(0,r.createElement)("a",{href:dt(e.product_url),target:"_blank",rel:"noreferrer"},(0,r.createElement)("span",{className:"woocommerce-marketplace__my-subscriptions__product-icon"},t)),(0,r.createElement)("a",{href:dt(e.product_url),className:"woocommerce-marketplace__my-subscriptions__product-name",target:"_blank",rel:"noreferrer"},e.product_name),(0,r.createElement)("span",{className:"woocommerce-marketplace__my-subscriptions__product-statuses"},e.is_shared&&(0,r.createElement)(it,{text:(0,s.__)("Shared with you","woocommerce"),level:We.Info,explanation:(0,r.createInterpolateElement)((0,s.sprintf)((0,s.__)("This subscription was shared by <email>%s</email>. <link>Learn more</link>.","woocommerce"),e.owner_email),{email:(0,r.createElement)("strong",{style:{overflowWrap:"anywhere"}},"email"),link:(0,r.createElement)("a",{href:N,rel:"nofollow noopener noreferrer"},"Learn more")})}))),value:e.product_name}}function wt(e){const t=e.expires;if(!0===e.local.installed&&""===e.product_key)return{display:"",value:""};let o=(0,s.__)("Never expires","woocommerce");return t&&(o=(0,Ye.gmdateI18n)("j M, Y",new Date(1e3*t))),{display:(0,r.createElement)("span",{className:"woocommerce-marketplace__my-subscriptions__expiry-date"},o),value:t}}function gt(e,t){return{display:function(){const o=function(e,t){return""===e.product_key?{text:(0,s.__)("No subscription","woocommerce"),level:We.Error,explanation:(0,r.createInterpolateElement)((0,s.__)("To receive updates and support, please <purchase>purchase</purchase> a subscription or use a subscription from another account by <sharing>sharing</sharing> or <transferring>transferring</transferring>.","woocommerce"),{purchase:(0,r.createElement)("a",{href:q(e),rel:"nofollow noopener noreferrer"},"renew"),sharing:(0,r.createElement)("a",{href:N,rel:"nofollow noopener noreferrer"},"sharing"),transferring:(0,r.createElement)("a",{href:k,rel:"nofollow noopener noreferrer"},"sharing")})}:e.expired?{text:(0,s.__)("Expired","woocommerce"),level:We.Error,explanation:(0,r.createInterpolateElement)((0,s.__)("To receive updates and support, please <renew>renew</renew> this subscription or use a subscription from another account by <sharing>sharing</sharing> or <transferring>transferring</transferring>.","woocommerce"),{renew:(0,r.createElement)("a",{href:K(e),rel:"nofollow noopener noreferrer"},"renew"),sharing:(0,r.createElement)("a",{href:N,rel:"nofollow noopener noreferrer"},"sharing"),transferring:(0,r.createElement)("a",{href:k,rel:"nofollow noopener noreferrer"},"sharing")})}:e.expiring&&!e.autorenew?{text:(0,s.__)("Expires soon","woocommerce"),level:We.Error,explanation:(0,r.createInterpolateElement)((0,s.__)("To receive updates and support, please <renew>renew</renew> this subscription before it expires or use a subscription from another account by <sharing>sharing</sharing> or <transferring>transferring</transferring>.","woocommerce"),{renew:(0,r.createElement)("a",{href:$(e),rel:"nofollow noopener noreferrer"},"renew"),sharing:(0,r.createElement)("a",{href:N,rel:"nofollow noopener noreferrer"},"sharing"),transferring:(0,r.createElement)("a",{href:k,rel:"nofollow noopener noreferrer"},"sharing")})}:!("installed"!==t||!e.local.installed||e.active)&&{text:(0,s.__)("Not connected","woocommerce"),level:We.Warning,explanation:(0,s.__)("To receive updates and support, please connect your subscription to this store.","woocommerce")}}(e,t);var c;if(o)return(0,r.createElement)(it,{text:o.text,level:o.level,explanation:null!==(c=o.explanation)&&void 0!==c?c:"",explanationOnHover:!0});let a;return a=e.lifetime?(0,s.__)("Lifetime","woocommerce"):e.autorenew?(0,s.__)("Active","woocommerce"):(0,s.__)("Cancelled","woocommerce"),a}()}}function yt(e,t){return{display:pt(e,t)}}function vt(e){let t=null;return""===e.product_key?t=(0,r.createElement)(tt,{subscription:e}):e.expired&&!e.lifetime?t=(0,r.createElement)(Xe,{subscription:e}):!1===e.local.installed&&!1===e.subscription_installed?t=(0,r.createElement)(Je,{subscription:e}):!1===e.active&&!0===e.subscription_available?t=(0,r.createElement)($e,{subscription:e,variant:"link"}):e.autorenew||e.lifetime||(t=(0,r.createElement)(et,{subscription:e})),{display:(0,r.createElement)("div",{className:"woocommerce-marketplace__my-subscriptions__actions"},t,(0,r.createElement)(mt,{subscription:e}))}}function ht(e,t){return[_t(e),wt(e),gt(e,t),yt(e,t),vt(e)]}const Et="data:image/svg+xml;base64,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",bt="woocommerce-marketplace-refresh-subscriptions";function ft(){const{refreshSubscriptions:e}=(0,r.useContext)(Re),[t,o]=(0,r.useState)(!1),c=()=>{t||(Y(bt),o(!0),e().then((()=>{G(bt,(0,s.__)("Subscriptions refreshed.","woocommerce"),L.Success)})).catch((e=>{G(bt,(0,s.sprintf)((0,s.__)("Error refreshing subscriptions: %s","woocommerce"),e.message),L.Error,{actions:[{label:(0,s.__)("Try again","woocommerce"),onClick:c}]})})).finally((()=>{o(!1)})))};return(0,r.createElement)(l.Button,{className:"woocommerce-marketplace__refresh-subscriptions",onClick:c,isBusy:t},(0,r.createElement)("img",{src:Et,alt:(0,s.__)("Refresh subscriptions","woocommerce"),className:"woocommerce-marketplace__refresh-subscriptions-icon"}),(0,s.__)("Refresh","woocommerce"))}const kt="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9ImFsZXJ0Ij4KPHBhdGggaWQ9IlZlY3RvciIgZD0iTTEyIDIwQzE2LjQxODMgMjAgMjAgMTYuNDE4MyAyMCAxMkMyMCA3LjU4MTcyIDE2LjQxODMgNCAxMiA0QzcuNTgxNzIgNCA0IDcuNTgxNzIgNCAxMkM0IDE2LjQxODMgNy41ODE3MiAyMCAxMiAyMFoiIHN0cm9rZT0iI0NDMTgxOCIgc3Ryb2tlLXdpZHRoPSIxLjUiLz4KPHBhdGggaWQ9IlZlY3Rvcl8yIiBkPSJNMTMgN0gxMVYxM0gxM1Y3WiIgZmlsbD0iI0NDMTgxOCIvPgo8cGF0aCBpZD0iVmVjdG9yXzMiIGQ9Ik0xMyAxNUgxMVYxN0gxM1YxNVoiIGZpbGw9IiNDQzE4MTgiLz4KPC9nPgo8L3N2Zz4K";function Nt(){const e=(0,I.useSelect)((e=>e(z).notices()),[]),t=e=>e.options?.actions?e.options?.actions.map((e=>({...e,variant:"link",className:"is-link"}))):[],o=[];for(const c of e)o.push((0,r.createElement)(l.Notice,{className:"woocommerce-marketplace__notice--error",status:c.status,onRemove:()=>Y(c.productKey),key:c.productKey,actions:t(c)},(0,r.createElement)("img",{src:kt,alt:"",width:24,height:24}),c.message));return(0,r.createElement)(r.Fragment,null,o)}function Mt(e){var t;const o=new URL(J());return e.install&&o.searchParams.set("install",e.install),(0,r.createElement)(l.Button,{href:o.href,variant:null!==(t=e.variant)&&void 0!==t?t:"secondary"},(0,s.__)("Connect Account","woocommerce"))}function Ct(){const e=(0,oe.useQuery)(),t=e?.install,o=(0,c.O3)("wccomHelper",{}),a=!!o?.isConnected,[n,i]=(0,r.useState)(!1),[m,u]=(0,r.useState)(!1),{subscriptions:p,isLoading:d}=(0,r.useContext)(Re),_=p.find((e=>e.product_key===t)),w=(0,r.useCallback)((()=>{(0,oe.navigateTo)({url:(0,oe.getNewPath)({...e,install:void 0},v,{})})}),[e]);(0,r.useEffect)((()=>{d||(t&&a&&!d&&!_?(G(t,(0,s.sprintf)((0,s.__)("Could not find subscription with product key %s.","woocommerce"),t),L.Error),w()):i(!!t))}),[a,d,t,w,_]),(0,r.useEffect)((()=>{_&&_.local.installed&&u(!0)}),[_]);const g=()=>{w(),i(!1)};return n?(0,r.createElement)(l.Modal,{title:m?(0,s.__)("You are ready to go!","woocommerce"):(0,s.__)("Add to store","woocommerce"),onRequestClose:g,focusOnMount:!0,className:"woocommerce-marketplace__header-account-modal has-size-medium",style:{borderRadius:4},overlayClassName:"woocommerce-marketplace__header-account-modal-overlay"},(()=>{if(!a)return(0,r.createElement)(l.Notice,{status:"warning",isDismissible:!1},(0,s.__)("In order to install a product, you need to first connect your account.","woocommerce"));if(_){const e=m?(0,s.__)("Keep the momentum going and start setting up your extension.","woocommerce"):(0,s.__)("Would you like to install this extension?","woocommerce");return(0,r.createElement)(r.Fragment,null,(0,r.createElement)("p",{className:"woocommerce-marketplace__header-account-modal-text"},e),(0,r.createElement)(ge,{product:F(_),small:!0,tracksData:{position:1,group:"subscriptions",label:"install"}}))}})(),(()=>{const e=[];return m?(e.push((0,r.createElement)(l.Button,{variant:"secondary",href:_?.documentation_url,target:"_blank",className:"woocommerce-marketplace__header-account-modal-button",key:"docs"},(0,s.__)("View docs","woocommerce"))),e.push((0,r.createElement)(l.Button,{variant:"primary",href:M,className:"woocommerce-marketplace__header-account-modal-button",key:"plugin-list"},(0,s.__)("View in Plugins","woocommerce")))):(e.push((0,r.createElement)(l.Button,{variant:"tertiary",onClick:g,className:"woocommerce-marketplace__header-account-modal-button",key:"cancel"},(0,s.__)("Cancel","woocommerce"))),a?_&&e.push((0,r.createElement)(Je,{subscription:_,variant:"primary",onError:g,key:"install"})):e.push((0,r.createElement)(Mt,{variant:"primary",install:t,key:"connect"}))),(0,r.createElement)(l.ButtonGroup,{className:"woocommerce-marketplace__header-account-modal-button-group"},e)})()):null}function xt(){const{subscriptions:e,isLoading:t}=(0,r.useContext)(Re),o=(0,c.O3)("wccomHelper",{}),a=(0,r.createInterpolateElement)((0,s.__)("WooCommerce.com extensions and themes installed on this store. To see all your subscriptions go to <a>your account<custom_icon /></a> on WooCommerce.com.","woocommerce"),{a:(0,r.createElement)("a",{href:"https://woocommerce.com/my-account/my-subscriptions",target:"_blank",rel:"nofollow noopener noreferrer"},"your account"),custom_icon:(0,r.createElement)(p.Z,{icon:d.Z,size:12})}),n=e.filter((e=>e.subscription_installed)),i=e.filter((e=>!e.subscription_installed&&o?.wooUpdateManagerPluginSlug!==e.product_slug&&!e.maxed));if(!o?.isConnected){const e=(0,s.__)("Connect your store to WooCommerce.com using the WooCommerce.com Update Manager. Once connected, you'll be able to manage your subscriptions, receive product updates, and access streamlined support from this screen.","woocommerce");return(0,r.createElement)("div",{className:"woocommerce-marketplace__my-subscriptions--connect"},(0,r.createElement)(Ct,null),(0,r.createElement)("div",{className:"woocommerce-marketplace__my-subscriptions__icon"}),(0,r.createElement)("h2",{className:"woocommerce-marketplace__my-subscriptions__header"},(0,s.__)("Manage your subscriptions","woocommerce")),(0,r.createElement)("p",{className:"woocommerce-marketplace__my-subscriptions__description"},e),(0,r.createElement)(l.Button,{href:J(),variant:"primary"},(0,s.__)("Connect your store","woocommerce")))}return(0,r.createElement)("div",{className:"woocommerce-marketplace__my-subscriptions"},(0,r.createElement)(Ct,null),(0,r.createElement)("section",{className:"woocommerce-marketplace__my-subscriptions__notices"},(0,r.createElement)(Nt,null)),(0,r.createElement)("section",{className:"woocommerce-marketplace__my-subscriptions-section woocommerce-marketplace__my-subscriptions__installed"},(0,r.createElement)("header",{className:"woocommerce-marketplace__my-subscriptions__header"},(0,r.createElement)("div",{className:"woocommerce-marketplace__my-subscriptions__header-content"},(0,r.createElement)("h2",{className:"woocommerce-marketplace__my-subscriptions__heading"},(0,s.__)("Installed on this store","woocommerce")),(0,r.createElement)("p",{className:"woocommerce-marketplace__my-subscriptions__table-description"},a)),(0,r.createElement)("div",{className:"woocommerce-marketplace__my-subscriptions__header-refresh"},(0,r.createElement)(ft,null))),(0,r.createElement)("div",{className:"woocommerce-marketplace__my-subscriptions__table-wrapper"},(0,r.createElement)(He,{isLoading:t,rows:n.map((e=>ht(e,"installed")))}))),i.length>0&&(0,r.createElement)("section",{className:"woocommerce-marketplace__my-subscriptions-section woocommerce-marketplace__my-subscriptions__available"},(0,r.createElement)("h2",{className:"woocommerce-marketplace__my-subscriptions__heading"},(0,s.__)("Available to use","woocommerce")),(0,r.createElement)("p",{className:"woocommerce-marketplace__my-subscriptions__table-description"},(0,s.__)("WooCommerce.com subscriptions you haven't used yet.","woocommerce")),(0,r.createElement)("div",{className:"woocommerce-marketplace__my-subscriptions__table-wrapper"},(0,r.createElement)(Ge,{isLoading:t,rows:i.map((e=>ht(e,"available")))}))))}var St=function(e){return e[e.notConnected=0]="notConnected",e[e.notInstalled=1]="notInstalled",e[e.installing=2]="installing",e[e.installedCanActivate=3]="installedCanActivate",e[e.installedCannotActivate=4]="installedCannotActivate",e[e.installFailed=5]="installFailed",e[e.activating=6]="activating",e[e.activated=7]="activated",e[e.activationFailed=8]="activationFailed",e}(St||{});const It=function(e){const[t,o]=(0,r.useState)(St.notInstalled),[n,i]=(0,r.useState)(),[m,u]=(0,r.useState)(),[p,d]=(0,r.useState)(),[_,w]=(0,r.useState)(),[g,y]=(0,r.useState)(),[h,E]=(0,r.useState)(!1),[b,f]=(0,r.useState)(),{addInstalledProduct:k}=(0,r.useContext)(a),N=(0,oe.useQuery)();function C(){o(St.notInstalled),f(void 0),(0,oe.navigateTo)({url:(0,oe.getNewPath)({...N,install:void 0,installProduct:void 0},v,{})})}return(0,r.useEffect)((()=>{const e=(0,c.O3)("wccomHelper",{});u(e?.installedProducts),d(e?.isConnected)}),[]),(0,r.useEffect)((()=>{if(E(!1),!N.installProduct)return;const t=parseInt(N.installProduct,10),r=e.products.find((e=>e.id===t));if(r){if(m&&m.find((e=>e===r.slug)))return;p?o(St.notInstalled):(o(St.notConnected),f({status:"warning",message:(0,s.__)("In order to install a product, you need to first connect your account.","woocommerce")})),E(!0),i(r)}}),[N,e.products,m,p]),n&&h?(0,r.createElement)(l.Modal,{title:t===St.activated?(0,s.__)("You are ready to go!","woocommerce"):(0,s.__)("Add to Store","woocommerce"),onRequestClose:C,focusOnMount:!0,className:"woocommerce-marketplace__header-account-modal has-size-medium",style:{borderRadius:4},overlayClassName:"woocommerce-marketplace__header-account-modal-overlay"},b&&(0,r.createElement)(l.Notice,{status:b.status,isDismissible:!1},b.message),(0,r.createElement)("p",{className:"woocommerce-marketplace__header-account-modal-text"},t===St.notConnected?"":t===St.installedCanActivate||t===St.activating?(0,s.__)("Extension successfully installed. Would you like to activate it?","woocommerce"):t===St.installedCannotActivate?(0,s.__)("Extension successfully installed but we can't activate it at the moment. Please visit the plugins page to see more.","woocommerce"):t===St.activated?(0,s.__)("Keep the momentum going and start setting up your extension.","woocommerce"):(0,s.__)("Would you like to install this extension?","woocommerce")),n&&(0,r.createElement)(ge,{product:n,small:!0,tracksData:{position:1,group:"install-flow",label:"install"}}),(0,r.createElement)(l.ButtonGroup,{className:"woocommerce-marketplace__header-account-modal-button-group"},t===St.activated?g?(0,r.createElement)(l.Button,{variant:"tertiary",href:g,className:"woocommerce-marketplace__header-account-modal-button",key:"docs"},(0,s.__)("View Docs","woocommerce")):(0,r.createElement)(r.Fragment,null):(0,r.createElement)(l.Button,{variant:"tertiary",onClick:C,className:"woocommerce-marketplace__header-account-modal-button",key:"cancel"},(0,s.__)("Cancel","woocommerce")),t===St.notConnected?(0,r.createElement)(Mt,{variant:"primary",key:"connect"}):t===St.installedCanActivate||t===St.activating?(0,r.createElement)(l.Button,{variant:"primary",onClick:function(){_&&(o(St.activating),(0,ue.recordEvent)("marketplace_activate_new_product_clicked",{product_id:n?n.id:0}),fetch(_).then((()=>{o(St.activated)})).catch((()=>{o(St.activationFailed),f({status:"error",message:(0,s.__)("Activation failed. Please try again from the plugins page.","woocommerce")})})))},key:"activate",isBusy:t===St.activating,disabled:t===St.activating},(0,s.__)("Activate","woocommerce")):t===St.activated||t===St.installedCannotActivate||t===St.activationFailed?(0,r.createElement)(l.Button,{variant:"primary",href:M,className:"woocommerce-marketplace__header-account-modal-button",key:"plugin-list"},(0,s.__)("View in Plugins","woocommerce")):(0,r.createElement)(l.Button,{variant:"primary",onClick:function(){var e;n&&n.id&&((0,ue.recordEvent)("marketplace_install_new_product_clicked",{product_id:n.id}),o(St.installing),(e=n.id,S()({path:"/wc/v3/marketplace/create-order",method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({product_id:e})})).then((e=>{if(!e.success)throw e;var t;return(0,I.dispatch)(qe).startInstalling(n.id),y(e.data.documentation_url),n.slug&&k(null!==(t=n.slug)&&void 0!==t?t:""),H(e.data.product_type,e.data.zip_slug).then((e=>{(0,I.dispatch)(qe).stopInstalling(n.id),e.data.activateUrl?(w(e.data.activateUrl),o(St.installedCanActivate)):o(St.installedCannotActivate)}))})).catch((e=>{var t;e.data.redirect_location?(f({status:"warning",message:(0,s.__)("We need your address to complete installing this product. We will redirect you to WooCommerce.com checkout. Afterwards, you will be able to install the product.","woocommerce")}),setTimeout((()=>{window.location.href=e.data.redirect_location}),5e3)):(o(St.installFailed),f({status:"error",message:null!==(t=e.data.message)&&void 0!==t?t:(0,s.__)("An error occurred. Please try again later.","woocommerce")}))})))},key:"install",isBusy:t===St.installing,disabled:t===St.installing||t===St.installFailed},(0,s.__)("Install","woocommerce")))):(0,r.createElement)(r.Fragment,null)};var Tt=o(19798),At=o(1642);function Lt(){const e="woo-connect-notice-marketplace-dismissed",t=(0,c.O3)("wccomHelper",{}),o=t?.woocomConnectNoticeType||"none";if("none"===o)return null;const a=localStorage.getItem(e),n=new Date(a||""),i=new Date;i.setMonth(i.getMonth()-1),(null===a||isNaN(n.valueOf())||i.valueOf()>n.valueOf())&&(localStorage.removeItem("wc-marketplaceNoticeClosed-woo-connect-notice"),localStorage.removeItem(e));let m="";"long"===o&&(m=m.concat((0,s.__)("Your store might be at risk as you are running old versions of WooCommerce plugins.","woocommerce")),m=m.concat(" ")),m=m.concat((0,s.__)("<strong>Connect your store to WooCommerce.com</strong> to get updates and streamlined support for your subscriptions.","woocommerce"));const u=W(J(),[["utm_source","pu"],["utm_campaign","pu_in_apps_screen_connect"]]);return(0,r.createElement)(At.Z,{id:"woo-connect-notice",description:m,isDismissible:!0,variant:"error",onClose:()=>{localStorage.setItem(e,(new Date).toString()),(0,ue.recordEvent)("woo_connect_notice_in_marketplace_dismissed")},onLoad:()=>{(0,ue.recordEvent)("woo_connect_notice_in_marketplace_shown")}},(0,r.createElement)(l.Button,{href:u,variant:"secondary",onClick:()=>((0,ue.recordEvent)("woo_connect_notice_in_marketplace_clicked"),!0)},(0,s.__)("Connect","woocommerce")))}function Dt(){const e=(0,c.O3)("wccomHelper",{});return e?.isConnected?e?.wooUpdateManagerActive||e?.wooUpdateManagerInstalled?e?.wooUpdateManagerInstalled&&!e?.wooUpdateManagerActive?(0,r.createElement)("section",{className:"woocommerce-marketplace__woo-update-manager-plugin__notices"},(0,r.createElement)(l.Notice,{status:"error",isDismissible:!1},(0,r.createElement)("span",{dangerouslySetInnerHTML:(0,at.ZP)((0,s.__)("Activate the <b>WooCommerce.com Update Manager</b> to continue receiving the updates and streamlined support included in your WooCommerce.com subscriptions.","woocommerce"))}),(0,r.createElement)("div",{className:"components-notice__buttons"},(0,r.createElement)(l.Button,{href:M,variant:"secondary"},(0,s.__)("Activate","woocommerce"))))):null:(0,r.createElement)("section",{className:"woocommerce-marketplace__woo-update-manager-plugin__notices"},(0,r.createElement)(l.Notice,{status:"error",isDismissible:!1},(0,r.createElement)("span",{dangerouslySetInnerHTML:(0,at.ZP)((0,s.__)("Please install the <b>WooCommerce.com Update Manager</b> to continue receiving the updates and streamlined support included in your WooCommerce.com subscriptions.<br/>Alternatively, you can download and install it manually.","woocommerce"))}),(0,r.createElement)("div",{className:"components-notice__buttons"},(0,r.createElement)(l.Button,{href:e?.wooUpdateManagerInstallUrl,variant:"secondary"},(0,s.__)("Install","woocommerce")),(0,r.createElement)(l.Button,{href:C,variant:"link"},(0,s.__)("Download","woocommerce"))))):null}function zt(e){const{type:t}=e,o=(0,c.O3)("wccomHelper",{}),a={"woo-subscription-expired-notice":{shown:"woo_subscription_expired_notice_in_marketplace_shown",clicked:"woo_subscription_expired_notice_in_marketplace_clicked",dismissed:"woo_subscription_expired_notice_in_marketplace_dismissed"},"woo-subscription-expiring-notice":{shown:"woo_subscription_expiring_notice_in_marketplace_shown",clicked:"woo_subscription_expiring_notice_in_marketplace_clicked",dismissed:"woo_subscription_expiring_notice_in_marketplace_dismissed"},"woo-subscription-missing-notice":{shown:"woo_subscription_missing_notice_in_marketplace_shown",clicked:"woo_subscription_missing_notice_in_marketplace_clicked",dismissed:"woo_subscription_missing_notice_in_marketplace_dismissed"}};let n=null,s="";const i=o?.dismissNoticeNonce||"";if("expired"===t)n=o?.subscription_expired_notice||{},s="woo-subscription-expired-notice";else if("expiring"===t)n=o?.subscription_expiring_notice||{},s="woo-subscription-expiring-notice";else{if("missing"!==t)return null;n=o?.subscription_missing_notice||{},s="woo-subscription-missing-notice"}return o.isConnected&&n?.description?(0,r.createElement)(At.Z,{id:s,description:n.description,isDismissible:!0,variant:"error",onClose:()=>{(0,ue.recordEvent)(a[s].dismissed);const e={notice_id:s,dismiss_notice_nonce:i};S()({path:"/wc-admin/notice/dismiss",method:"POST",data:e})},onLoad:function(){(0,ue.recordEvent)(a[s].shown)}},(0,r.createElement)(l.Button,{href:n.button_link,variant:"secondary",onClick:function(){(0,ue.recordEvent)(a[s].clicked)}},n.button_text)):null}function Ot(e){const{onLoadMore:t,isBusy:o,disabled:c}=e;return o&&(0,me.speak)((0,s.__)("Loading more products","woocommerce")),(0,r.createElement)(l.Button,{className:"woocommerce-marketplace__load-more",variant:"secondary",onClick:function(){(0,ue.queueRecordEvent)("marketplace_load_more_button_clicked",{}),t()},isBusy:o,disabled:c},(0,s.__)("Load more","woocommerce"))}function Pt(){const e=(0,r.useContext)(a),[t,o]=(0,r.useState)([]),[n,i]=(0,r.useState)([]),[l,m]=(0,r.useState)(1),[u,p]=(0,r.useState)(1),[d,_]=(0,r.useState)(1),[w,g]=(0,r.useState)(1),[y,v]=(0,r.useState)(1),[h,E]=(0,r.useState)(0),[b,f]=(0,r.useState)(!1),{isLoading:k,setIsLoading:N,selectedTab:M,setHasBusinessServices:C,setSearchResultsCount:x}=e,S=(0,oe.useQuery)(),I=e=>{(0,me.speak)((0,s.sprintf)((0,s.__)("%d products found","woocommerce"),e))},A=(0,r.useCallback)((()=>{f(!0);const e=new URLSearchParams,t=new AbortController;S.category&&"_all"!==S.category&&e.append("category",S.category),"themes"!==S.tab&&"business-services"!==S.tab||e.append("category",S.tab),S.term&&e.append("term",S.term);const r=(0,c.O3)("wccomHelper",!1);return r.storeCountry&&e.append("country",r.storeCountry),e.append("page",(l+1).toString()),Z(e,t.signal).then((e=>{o((t=>{const o=Array.isArray(t[0])?t.flat():t,r=e.products.filter((e=>!o.some((t=>t.id===e.id))));var c;return r.length>0&&E(null!==(c=r[0].id)&&void 0!==c?c:0),[...o,...r]})),(0,me.speak)((0,s.__)("More products loaded","woocommerce")),m((e=>e+1)),f(!1)})).catch((()=>{(0,me.speak)((0,s.__)("Error loading more products","woocommerce"))})).finally((()=>{f(!1)})),()=>{t.abort()}}),[l,S.category,S.term,S.tab,f]);return(0,r.useEffect)((()=>{if(l>1)return;const e=[{category:"extensions",type:T.extension},{category:"themes",type:T.theme},{category:"business-services",type:T.businessService}],t=e.map((()=>new AbortController));if(N(!0),o([]),S.category&&"_all"!==S.category){const e=new URLSearchParams;e.append("category",S.category),S.term&&e.append("term",S.term);const r=(0,c.O3)("wccomHelper",!1);r.storeCountry&&e.append("country",r.storeCountry),Z(e,t[0].signal).then((e=>{o(e.products),p(e.totalPages),x({[S.tab]:e.totalProducts}),I(e.totalProducts)})).catch((()=>{o([])})).finally((()=>{N(!1)}))}else Promise.all(e.map((({category:e,type:o},r)=>{const a=new URLSearchParams;"extensions"!==e&&a.append("category",e),S.term&&a.append("term",S.term);const n=(0,c.O3)("wccomHelper",!1);return n.storeCountry&&a.append("country",n.storeCountry),Z(a,t[r].signal).then((t=>{const r=((e,t)=>e.map((e=>({...e,type:t}))))(t.products,o);return"business-services"===e&&C(r.length>0),{products:r,totalPages:t.totalPages,totalProducts:t.totalProducts,type:o}}))}))).then((e=>{const t=e.flatMap((e=>e.products));o(t),x({extensions:e.find((e=>"extension"===e.type))?.totalProducts,themes:e.find((e=>"theme"===e.type))?.totalProducts,"business-services":e.find((e=>"business-service"===e.type))?.totalProducts}),e.forEach((e=>{switch(e.type){case T.extension:_(e.totalPages);break;case T.theme:g(e.totalPages);break;case T.businessService:v(e.totalPages)}})),I(e.reduce(((e,t)=>e+t.totalProducts),0))})).catch((()=>{o([])})).finally((()=>{N(!1)}));return()=>{t.forEach((e=>{e.abort()}))}}),[S.tab,S.term,S.category,C,N,x,l]),(0,r.useEffect)((()=>{let e;switch(M){case"extensions":e=t.filter((e=>e.type===T.extension));break;case"themes":e=t.filter((e=>e.type===T.theme));break;case"business-services":e=t.filter((e=>e.type===T.businessService));break;default:e=[]}i(e)}),[M,t]),(0,r.useEffect)((()=>{const e={view:S?.tab,search_term:S?.term,product_type:S?.section,category:S?.category};Me(e),function(e){if(e.product_type)return;let t="extensions_view";const o=e.view||"_featured",r=e.search_term||null,c=e.category||null,a={...o&&{section:o},...r&&{search_term:r},version:"2"};switch(o){case"extensions":a.section=c||"_all";break;case"themes":a.section="themes";break;case"my-subscriptions":t="subscriptions_view",a.section="helper"}(0,ue.recordEvent)(t,a)}(e)}),[S?.tab,S?.term,S?.section,S?.category]),(0,r.useEffect)((()=>{m(1),E(0)}),[M,S?.category,S?.term]),(0,r.useEffect)((()=>{h&&setTimeout((()=>{const e=document.getElementById(`product-${h}`);e&&e.focus()}),0)}),[h]),(0,r.createElement)("div",{className:"woocommerce-marketplace__content"},(0,r.createElement)(Tt.Z,{format:"promo-card"}),(0,r.createElement)(Tt.Z,{format:"notice"}),(0,r.createElement)(It,{products:n}),"business-services"!==M&&"my-subscriptions"!==M&&(0,r.createElement)(Lt,null),"business-services"!==M&&(0,r.createElement)(Dt,null),"business-services"!==M&&(0,r.createElement)(zt,{type:"expired"}),"business-services"!==M&&(0,r.createElement)(zt,{type:"expiring"}),(()=>{switch(M){case"extensions":case"themes":case"business-services":return(0,r.createElement)(Ze,{products:n,categorySelector:!0,type:B(M)});case"discover":return(0,r.createElement)(Ce,null);case"my-subscriptions":return(0,r.createElement)(Be,null,(0,r.createElement)(xt,null));default:return(0,r.createElement)(r.Fragment,null)}})(),!k&&(()=>{if(S.category&&"_all"!==S.category)return l<u;switch(M){case"extensions":return l<d;case"themes":return l<w;case"business-services":return l<y;default:return!1}})()&&(0,r.createElement)(Ot,{onLoadMore:A,isBusy:b,disabled:b}))}var jt=o(69441);const Ut=(0,r.createElement)(m.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(m.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M6.68822 16.625L5.5 17.8145L5.5 5.5L18.5 5.5L18.5 16.625L6.68822 16.625ZM7.31 18.125L19 18.125C19.5523 18.125 20 17.6773 20 17.125L20 5C20 4.44772 19.5523 4 19 4H5C4.44772 4 4 4.44772 4 5V19.5247C4 19.8173 4.16123 20.086 4.41935 20.2237C4.72711 20.3878 5.10601 20.3313 5.35252 20.0845L7.31 18.125ZM16 9.99997H8V8.49997H16V9.99997ZM8 14H13V12.5H8V14Z"})),Zt=(0,r.createElement)(m.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(m.Path,{d:"M12 3.176l6.75 3.068v4.574c0 3.9-2.504 7.59-6.035 8.755a2.283 2.283 0 01-1.43 0c-3.53-1.164-6.035-4.856-6.035-8.755V6.244L12 3.176zM6.75 7.21v3.608c0 3.313 2.145 6.388 5.005 ************.331.053.49 0 2.86-.942 5.005-4.017 5.005-7.33V7.21L12 4.824 6.75 7.21z",fillRule:"evenodd",clipRule:"evenodd"})),Rt=(0,r.createElement)(m.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(m.Path,{d:"M15.5 9.5a1 1 0 100-2 1 1 0 000 2zm0 1.5a2.5 2.5 0 100-5 2.5 2.5 0 000 5zm-2.25 6v-2a2.75 2.75 0 00-2.75-2.75h-4A2.75 2.75 0 003.75 15v2h1.5v-2c0-.69.56-1.25 1.25-1.25h4c.69 0 1.25.56 1.25 1.25v2h1.5zm7-2v2h-1.5v-2c0-.69-.56-1.25-1.25-1.25H15v-1.5h2.5A2.75 2.75 0 0120.25 15zM9.5 8.5a1 1 0 11-2 0 1 1 0 012 0zm1.5 0a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z",fillRule:"evenodd"}));function Bt(e){const{icon:t,title:o,description:c}=e;return(0,r.createElement)("div",{className:"woocommerce-marketplace__icon-group"},(0,r.createElement)("div",{className:"woocommerce-marketplace__icon-group-headline"},(0,r.createElement)(p.Z,{icon:t,size:20,className:"woocommerce-marketplace__icon-group-icon"}),(0,r.createElement)("h3",{className:"woocommerce-marketplace__icon-group-title"},o)),(0,r.createElement)("p",{className:"woocommerce-marketplace__icon-group-description"},c))}const Qt=(0,r.createInterpolateElement)((0,s.__)("30-day <a>money-back guarantee</a>","woocommerce"),{a:(0,r.createElement)("a",{href:y+"/refund-policy/"})}),Vt=(0,r.createInterpolateElement)((0,s.__)("<a>Get help</a> when you need it","woocommerce"),{a:(0,r.createElement)("a",{href:y+"/docs/"})}),Ht=(0,r.createInterpolateElement)((0,s.__)("<a>Products</a> you can trust","woocommerce"),{a:(0,r.createElement)("a",{href:y+"/products/"})});function Gt(){return(0,r.createElement)("div",{className:"woocommerce-marketplace__footer-content"},(0,r.createElement)("h2",{className:"woocommerce-marketplace__footer-title"},(0,s.__)("Hundreds of vetted products and services. Unlimited potential.","woocommerce")),(0,r.createElement)("div",{className:"woocommerce-marketplace__footer-columns"},(0,r.createElement)(Bt,{icon:jt.Z,title:Qt,description:(0,s.__)("If you change your mind within 30 days of your purchase, we'll give you a full refund — hassle-free.","woocommerce")}),(0,r.createElement)(Bt,{icon:Ut,title:Vt,description:(0,s.__)("With detailed documentation and a global support team, help is always available if you need it.","woocommerce")}),(0,r.createElement)(Bt,{icon:Zt,title:Ht,description:(0,s.__)("Everything in the Marketplace has been built by our own team or by our trusted partners, so you can be sure of its quality.","woocommerce")}),(0,r.createElement)(Bt,{icon:Rt,title:(0,s.__)("Support the ecosystem","woocommerce"),description:(0,s.__)("Our team and partners are continuously improving your extensions, themes, and WooCommerce experience.","woocommerce")})))}function Yt(){return(0,r.createElement)("div",{className:"woocommerce-marketplace__footer"},(0,r.createElement)(Gt,null))}function Ft(){const{selectedTab:e}=(0,r.useContext)(a),t="woocommerce-marketplace"+(e?" woocommerce-marketplace--"+e:"");return(0,r.createElement)("div",{className:t},(0,r.createElement)(le,null),(0,r.createElement)(Pt,null),(0,r.createElement)(Yt,null))}function Wt(){return(0,r.createElement)(n,null,(0,r.createElement)(Ft,null))}}}]);