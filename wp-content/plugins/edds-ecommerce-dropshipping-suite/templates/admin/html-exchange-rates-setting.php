<?php
defined( 'ABSPATH' ) || exit;
$settings        = $settings ?? TMDS_DATA::get_instance();
$current_currency = get_woocommerce_currency();
$import_currency_rate = $settings->get_params('import_currency_rate');
if (!is_array($import_currency_rate)){
    $import_currency_rate =[];
}
$accept_currency = $settings::get_temu_currencies();
if (empty($import_currency_rate)){
    if (isset($accept_currency[$current_currency])){
        $import_currency_rate[$current_currency] = 1;
    }else{
        $import_currency_rate['USD'] =1;
    }
}
?>
<table class="vi-ui celled table price-exchange-rates price-rule" >
	<thead>
	<tr>
		<th><?php esc_html_e( 'Temu currency', 'tmds-dropshipping-for-temu-and-woo' ) ?></th>
		<th><?php printf(esc_html__( 'Exchange rate( your store currency: %s )', 'tmds-dropshipping-for-temu-and-woo' ), wp_kses_post($current_currency)) // phpcs:ignore WordPress.WP.I18n.MissingTranslatorsComment ?></th>
		<th></th>
	</tr>
	</thead>
    <tbody class="tmds-price-exchange-rates-container tmds-price-rule-container">
    <?php
    if (!empty($import_currency_rate)){
        foreach ($import_currency_rate as $currency => $rate){
            ?>
            <tr class="tmds-price-rule-row">
                <td>
                    <?php
                    $settings::villatheme_render_field('import_currency_rate',[
                            'type'=>'select',
                            'empty_name_field'=>1,
                            'is_search'=>1,
                            'value'=>$currency,
                            'options'=>$accept_currency,
                    ]);
                    ?>
                </td>
                <td>
                    <input type="number" min="0" step="any"
                           value="<?php echo esc_attr( $rate); ?>" name="import_currency_rate[<?php echo esc_attr($currency)?>]"
                           data-name="import_currency_rate[{currency_code}]"
                           class="tmds-price-exchange-rate tmds-input-reset">
                </td>
                <td>
                    <div>
                        <span class="vi-ui button icon negative mini tmds-price-rule-remove"><i class="icon trash"> </i></span>
                    </div>
                </td>
            </tr>
            <?php
        }
    }
    ?>
    </tbody>
</table>
<span class="tmds-exchange-rate-add tmds-price-rule-add vi-ui button icon positive mini">
    <i class="icon add"> </i>
</span>