!function(F,A,q){"use strict";A=void 0!==A&&A.Math==Math?A:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),F.fn.accordion=function(a){var v,s=F(this),b=(new Date).getTime(),y=[],C=a,O="string"==typeof C,x=[].slice.call(arguments,1);A.requestAnimationFrame||A.mozRequestAnimationFrame||A.webkitRequestAnimationFrame||A.msRequestAnimationFrame;return s.each(function(){var e,c=F.isPlainObject(a)?F.extend(!0,{},F.fn.accordion.settings,a):F.extend({},F.fn.accordion.settings),u=c.className,n=c.namespace,d=c.selector,l=c.error,t="."+n,i="module-"+n,o=s.selector||"",g=F(this),f=g.find(d.title),m=g.find(d.content),r=this,p=g.data(i),h={initialize:function(){h.debug("Initializing",g),h.bind.events(),c.observeChanges&&h.observeChanges(),h.instantiate()},instantiate:function(){p=h,g.data(i,h)},destroy:function(){h.debug("Destroying previous instance",g),g.off(t).removeData(i)},refresh:function(){f=g.find(d.title),m=g.find(d.content)},observeChanges:function(){"MutationObserver"in A&&((e=new MutationObserver(function(e){h.debug("DOM tree modified, updating selector cache"),h.refresh()})).observe(r,{childList:!0,subtree:!0}),h.debug("Setting up mutation observer",e))},bind:{events:function(){h.debug("Binding delegated events"),g.on(c.on+t,d.trigger,h.event.click)}},event:{click:function(){h.toggle.call(this)}},toggle:function(e){var n=e!==q?"number"==typeof e?f.eq(e):F(e).closest(d.title):F(this).closest(d.title),t=n.next(m),i=t.hasClass(u.animating),o=t.hasClass(u.active),a=o&&!i,s=!o&&i;h.debug("Toggling visibility of content",n),a||s?c.collapsible?h.close.call(n):h.debug("Cannot close accordion content collapsing is disabled"):h.open.call(n)},open:function(e){var n=e!==q?"number"==typeof e?f.eq(e):F(e).closest(d.title):F(this).closest(d.title),t=n.next(m),i=t.hasClass(u.animating);t.hasClass(u.active)||i?h.debug("Accordion already open, skipping",t):(h.debug("Opening accordion content",n),c.onOpening.call(t),c.onChanging.call(t),c.exclusive&&h.closeOthers.call(n),n.addClass(u.active),t.stop(!0,!0).addClass(u.animating),c.animateChildren&&(F.fn.transition!==q&&g.transition("is supported")?t.children().transition({animation:"fade in",queue:!1,useFailSafe:!0,debug:c.debug,verbose:c.verbose,duration:c.duration}):t.children().stop(!0,!0).animate({opacity:1},c.duration,h.resetOpacity)),t.slideDown(c.duration,c.easing,function(){t.removeClass(u.animating).addClass(u.active),h.reset.display.call(this),c.onOpen.call(this),c.onChange.call(this)}))},close:function(e){var n=e!==q?"number"==typeof e?f.eq(e):F(e).closest(d.title):F(this).closest(d.title),t=n.next(m),i=t.hasClass(u.animating),o=t.hasClass(u.active);!o&&!(!o&&i)||o&&i||(h.debug("Closing accordion content",t),c.onClosing.call(t),c.onChanging.call(t),n.removeClass(u.active),t.stop(!0,!0).addClass(u.animating),c.animateChildren&&(F.fn.transition!==q&&g.transition("is supported")?t.children().transition({animation:"fade out",queue:!1,useFailSafe:!0,debug:c.debug,verbose:c.verbose,duration:c.duration}):t.children().stop(!0,!0).animate({opacity:0},c.duration,h.resetOpacity)),t.slideUp(c.duration,c.easing,function(){t.removeClass(u.animating).removeClass(u.active),h.reset.display.call(this),c.onClose.call(this),c.onChange.call(this)}))},closeOthers:function(e){var n,t,i=e!==q?f.eq(e):F(this).closest(d.title),o=i.parents(d.content).prev(d.title),a=i.closest(d.accordion),s=d.title+"."+u.active+":visible",l=d.content+"."+u.active+":visible",r=c.closeNested?(n=a.find(s).not(o)).next(m):(n=a.find(s).not(o),t=a.find(l).find(s).not(o),(n=n.not(t)).next(m));0<n.length&&(h.debug("Exclusive enabled, closing other content",n),n.removeClass(u.active),r.removeClass(u.animating).stop(!0,!0),c.animateChildren&&(F.fn.transition!==q&&g.transition("is supported")?r.children().transition({animation:"fade out",useFailSafe:!0,debug:c.debug,verbose:c.verbose,duration:c.duration}):r.children().stop(!0,!0).animate({opacity:0},c.duration,h.resetOpacity)),r.slideUp(c.duration,c.easing,function(){F(this).removeClass(u.active),h.reset.display.call(this)}))},reset:{display:function(){h.verbose("Removing inline display from element",this),F(this).css("display",""),""===F(this).attr("style")&&F(this).attr("style","").removeAttr("style")},opacity:function(){h.verbose("Removing inline opacity from element",this),F(this).css("opacity",""),""===F(this).attr("style")&&F(this).attr("style","").removeAttr("style")}},setting:function(e,n){if(h.debug("Changing setting",e,n),F.isPlainObject(e))F.extend(!0,c,e);else{if(n===q)return c[e];F.isPlainObject(c[e])?F.extend(!0,c[e],n):c[e]=n}},internal:function(e,n){if(h.debug("Changing internal",e,n),n===q)return h[e];F.isPlainObject(e)?F.extend(!0,h,e):h[e]=n},debug:function(){!c.silent&&c.debug&&(c.performance?h.performance.log(arguments):(h.debug=Function.prototype.bind.call(console.info,console,c.name+":"),h.debug.apply(console,arguments)))},verbose:function(){!c.silent&&c.verbose&&c.debug&&(c.performance?h.performance.log(arguments):(h.verbose=Function.prototype.bind.call(console.info,console,c.name+":"),h.verbose.apply(console,arguments)))},error:function(){c.silent||(h.error=Function.prototype.bind.call(console.error,console,c.name+":"),h.error.apply(console,arguments))},performance:{log:function(e){var n,t;c.performance&&(t=(n=(new Date).getTime())-(b||n),b=n,y.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:r,"Execution Time":t})),clearTimeout(h.performance.timer),h.performance.timer=setTimeout(h.performance.display,500)},display:function(){var e=c.name+":",t=0;b=!1,clearTimeout(h.performance.timer),F.each(y,function(e,n){t+=n["Execution Time"]}),e+=" "+t+"ms",o&&(e+=" '"+o+"'"),(console.group!==q||console.table!==q)&&0<y.length&&(console.groupCollapsed(e),console.table?console.table(y):F.each(y,function(e,n){console.log(n.Name+": "+n["Execution Time"]+"ms")}),console.groupEnd()),y=[]}},invoke:function(i,e,n){var o,a,t,s=p;return e=e||x,n=r||n,"string"==typeof i&&s!==q&&(i=i.split(/[\. ]/),o=i.length-1,F.each(i,function(e,n){var t=e!=o?n+i[e+1].charAt(0).toUpperCase()+i[e+1].slice(1):i;if(F.isPlainObject(s[t])&&e!=o)s=s[t];else{if(s[t]!==q)return a=s[t],!1;if(!F.isPlainObject(s[n])||e==o)return s[n]!==q?a=s[n]:h.error(l.method,i),!1;s=s[n]}})),F.isFunction(a)?t=a.apply(n,e):a!==q&&(t=a),F.isArray(v)?v.push(t):v!==q?v=[v,t]:t!==q&&(v=t),a}};O?(p===q&&h.initialize(),h.invoke(C)):(p!==q&&p.invoke("destroy"),h.initialize())}),v!==q?v:this},F.fn.accordion.settings={name:"Accordion",namespace:"accordion",silent:!1,debug:!1,verbose:!1,performance:!0,on:"click",observeChanges:!0,exclusive:!0,collapsible:!0,closeNested:!1,animateChildren:!0,duration:350,easing:"easeOutQuad",onOpening:function(){},onClosing:function(){},onChanging:function(){},onOpen:function(){},onClose:function(){},onChange:function(){},error:{method:"The method you called is not defined"},className:{active:"active",animating:"animating"},selector:{accordion:".accordion",title:".title",trigger:".title",content:".content"}},F.extend(F.easing,{easeOutQuad:function(e,n,t,i,o){return-i*(n/=o)*(n-2)+t}})}(jQuery,window,void document);