* {
  box-sizing: border-box;
}

.tmds-hidden {
  display: none !important;
}
.tmds-import-list-head {
  display: inline-flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: flex-end;
  .button {
    padding: 10px 8px !important;
    font-size: 11px !important;
  }
}

.tmds-delete-product-options-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99999;

  .tmds-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(1, 1, 1, 0.3);
  }

  .tmds-saving-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.6);
    z-index: 100;
    cursor: progress;
    display: none;
  }

  .tmds-delete-product-options-content {
    position: relative;
    z-index: 99;
    width: 100%;
    max-height: 90%;
    overflow: hidden;
    background: white;
    display: flex;
    flex-direction: column;
    max-width: 600px;
    border-radius: 10px;

    .tmds-delete-product-options-content-header {
      display: flex;
      padding: 10px 20px;
      justify-content: flex-start;
      position: relative;

      .tmds-delete-product-options-close {
        height: 30px;
        line-height: 30px;
        cursor: pointer;
        position: absolute;
        top: 50%;
        right: 20px;
        transform: translateY(-50%);

        &:before {
          font-family: Dashicons;
          content: "\f335";
          font-size: 30px;
          margin: 0;
          padding: 0;
        }
      }

      .tmds-delete-product-options-product-title {
        font-weight: 400;
      }

      h2 {
        margin-right: 30px;
      }
    }

    .tmds-delete-product-options-content-body {
      border-top: 1px solid #eaeaea;
      border-bottom: 1px solid #eaeaea;
      padding-bottom: 20px;

      .tmds-delete-product-options-content-body-row {
        padding-top: 20px;

        .tmds-delete-product-options-override-product-wrap .tmds-delete-product-options-override-product,
        .tmds-delete-product-options-delete-woo-product-wrap .tmds-delete-product-options-delete-woo-product {
          margin: 0 10px 0 0;
          padding: 10px;
        }

        .tmds-delete-product-options-override-product-wrap {
          flex-wrap: wrap;
          width: 100%;
          position: relative;

          .tmds-delete-product-options-override-product {
            width: 100%;
          }
        }

        .tmds-delete-product-options-override-product-wrap,
        .tmds-delete-product-options-delete-woo-product-wrap {
          padding: 0 20px;
          display: flex;
          flex-direction: row;
          align-items: center;
        }
      }
    }

    .tmds-delete-product-options-content-footer {
      display: flex;
      padding: 20px;
      justify-content: flex-end;

      .button {
        margin: 0 5px;
      }
    }
  }
}
.tmds-imported-list-wrap {
  .tmds-import-from-note {
    font-size: 0.9em;
  }
  .tmds-accordion {
    margin: 20px 0;
  }

  .vi-ui.accordion .vi-ui.attached.tabular.menu {
    padding-top: 0;
  }

  form p.search-box {
    float: right;
    margin: 0;
    position: relative;
    padding: 0 10px;
    text-align: right;
  }

  form p.search-box input.text.short {
    width: calc(100% - 130px);
    max-width: 300px;
  }

  form.tmds-imported-products-trash .tmds-imported-products-count-trash-container a,
  form.tmds-imported-products-publish .tmds-imported-products-count-publish-container a {
    font-weight: 600;
    color: #000;
  }
  .tmds-button-update-products-container{
    display: inline-block;
    padding-left: 10px;
  }

  .tmds-accordion-deleting .tmds-button-view-and-edit .vi-ui.button.tmds-button-deleting {
    display: inline-block;
  }

  .tmds-button-override-container .button,
  .tmds-button-view-and-edit .button {
    transition: font-size 300ms ease;
  }

  .tmds-accordion-deleting .button:not(.tmds-button-deleting) {
    font-size: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none;
  }

  .tmds-message .vi-ui.message {
    margin-bottom: 1em;
  }

  p.search-box {
    padding-right: 10px;
  }
  .tmds-button-view-and-edit .vi-ui.button.tmds-button-deleting {
    display: none;
  }
  .tmds-accordion-product-image-title-container {
    display: inline-block;
    width: 100%;
    max-width: calc(100% - 430px);
    vertical-align: middle;
  }

  .tmds-accordion-product-image-title {
    display: flex;
    align-items: center;
  }

  .tmds-accordion-product-title-container {
    width: calc(100% - 45px);
  }
  .tmds-import-data-image{
    width: 100%;
  }
  .vi-ui.styled.accordion .title {
    position: relative;

    .tmds-button-view-and-edit {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .tmds-accordion-product-title {
    font-weight: 400;
    text-overflow: ellipsis;
    overflow: hidden;
    max-height: 2em;
    white-space: nowrap;
  }

  .tmds-accordion-product-image {
    width: 36px;
    outline: 1px solid rgba(1, 1, 1, .1);
    margin: 0 5px;
  }

  .tmds-price-field {
    font-family: Lato, 'Helvetica Neue', Arial, Helvetica, sans-serif;
    margin: 0;
    outline: 0;
    -webkit-appearance: none;
    tap-highlight-color: rgba(255, 255, 255, 0);
    line-height: 1.2142em;
    padding: .67861429em 1em;
    font-size: 13px;
    background: #fff;
    border: 1px solid rgba(34, 36, 38, .15);
    color: rgba(0, 0, 0, .87);
    border-radius: .28571429rem;
    box-shadow: 0 0 0 0 transparent inset;
    -webkit-transition: color .1s ease, border-color .1s ease;
    transition: color .1s ease, border-color .1s ease;
  }

  .vi-ui.form input:not([type]),
  .vi-ui.form input[type=date], .vi-ui.form input[type=datetime-local], .vi-ui.form input[type=email], .vi-ui.form input[type=number], .vi-ui.form input[type=password], .vi-ui.form input[type=search], .vi-ui.form input[type=tel], .vi-ui.form input[type=time], .vi-ui.form input[type=text], .vi-ui.form input[type=url] {
    font-size: 13px;
  }


  .tmds-product-container {
    position: relative;

    .tmds-button-override-container {
      display: flex;
      justify-content: flex-end;
    }

    .tmds-product-image,
    .tmds-product-gallery .tmds-product-gallery-item {
      margin: 15px;
      outline: 2px solid rgba(228, 228, 228, 0.67);
      display: inline-flex;
      position: relative;
    }

    .tmds-product-gallery .tmds-product-gallery-item {
      &.tmds-selected-item {
        outline: 3px solid #7cc84f;

        .tmds-selected-item-icon-check:before {
          content: "\f147";
          font-family: Dashicons;
        }

        .tmds-selected-item-icon-check {
          outline: 3px solid #7cc84f;
          background: #ffffff;
        }
      }

      .tmds-selected-item-icon-check {
        width: 20px;
        height: 20px;
        position: absolute;
        left: 0;
        text-align: center;
      }

      .tmds-product-gallery-image {
        height: 150px;
      }
    }
  }

}
.tmds-import-list-wrap {

  .vi-ui.dropdown .menu > .item,
  .vi-ui.form,
  .vi-ui.form input:not([type]),
  .vi-ui.form input[type=date],
  .vi-ui.form input[type=datetime-local],
  .vi-ui.form input[type=email],
  .vi-ui.form input[type=number],
  .vi-ui.form input[type=password],
  .vi-ui.form input[type=search],
  .vi-ui.form input[type=tel],
  .vi-ui.form input[type=time],
  .vi-ui.form input[type=text],
  .vi-ui.form input[type=url] {
    font-size: 13px;
  }
  .tmds-import-data-image{
    width: 100%;
  }
  .vi-ui.attached.segment {
    max-height: 500px;
    overflow: auto;
    min-height: 250px;

    &.tmds-product-tab {
      max-height: unset;
      overflow: visible;
    }
  }

  .vi-ui .form-table th,
  .vi-ui .form-table td {
    text-align: center;
  }

  .vi-ui .form-table thead tr td,
  .vi-ui .form-table thead tr th {
    background: #f7f7f7;
  }

  .tmds-pagination-form {
    position: sticky !important;
    top: 32px !important;
    z-index: 999 !important;
    margin-bottom: 0 !important;
    border-bottom: none !important;
    border-radius: 4px 4px 0 0 !important;

    .tmds-button-import-all-container {
      text-align: center;
      float: left;

      .tmds-accordion-bulk-actions-container {
        display: none;
      }
    }

    .search-box {
      margin: 0;
      width: calc(100% - 650px);
      position: relative;
      padding: 0 10px;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      input.text.short {
        width: calc(100% - 130px);
        max-width: 300px;
      }
    }
  }

  /*Attributes tab*/
  .tmds-attributes-tab {
    table thead th {
      position: sticky;
      top: -1em;
    }

    table th,
    table td {
      text-align: center !important;
    }

    input {
      margin: 2px 0 !important;
    }

    tr td .tmds-attributes-attribute-values {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }

    tr td .tmds-attributes-attribute-values .tmds-attributes-attribute-value {
      display: inline-block;
      width: auto;
      min-width: unset !important;
    }

    th.tmds-attributes-attribute-col-position {
      width: 1%;
    }

    th.tmds-attributes-attribute-col-name,
    th.tmds-attributes-attribute-col-slug {
      min-width: 150px;
      width: 1%;
    }

    th.tmds-attributes-attribute-col-action {
      min-width: 120px;
      //width: 1%;
    }

    .tmds-attributes-button-edit-trash, .tmds-attributes-button-save-cancel {
      display: flex;
      justify-content: center;
    }

    .tmds-attributes-attribute-row:not(.tmds-attributes-attribute-editing) .tmds-attributes-button-save-cancel,
    .tmds-attributes-attribute-row.tmds-attributes-attribute-editing .tmds-attributes-attribute-remove,
    .tmds-attributes-attribute-row.tmds-attributes-attribute-editing .tmds-attributes-button-edit {
      display: none;
    }

    tr.tmds-attributes-attribute-row.tmds-attributes-attribute-removing td:first-child {
      border-left: 1px solid red !important;
    }

    tr.tmds-attributes-attribute-row.tmds-attributes-attribute-removing td:last-child {
      border-right: 1px solid red !important;
    }

    tr.tmds-attributes-attribute-row.tmds-attributes-attribute-removing td {
      border-top: 1px solid red !important;
      border-bottom: 1px solid red !important;
    }
  }

  .tmds-variation-image,
  .tmds-product-image,
  .tmds-product-gallery .tmds-product-gallery-item {
    margin: 15px;
    outline: 2px solid rgba(228, 228, 228, 0.67);
    display: inline-flex;
    position: relative;
    cursor: pointer;
  }
  .vi-ui.search.dropdown .menu{
    max-height: 10.68571429rem !important;
  }
  .tmds-product-gallery .tmds-product-gallery-item .tmds-product-gallery-image {
    max-height: 150px;
    min-width: 150px;
    max-width: 300px;
  }

  .tmds-selected-item {
    outline: 3px solid #7cc84f !important;
  }

  .tmds-variation-image.tmds-selected-item {
    outline: 2px solid #7cc84f !important;
  }

  .tmds-selected-item-icon-check {
    width: 20px;
    height: 20px;
    position: absolute;
    left: 0;
    text-align: center;
  }

  .tmds-variation-image img {
    max-width: 64px;
    max-height: 64px;
  }
  .tmds-variation-image.tmds-selected-item span.tmds-selected-item-icon-check {
    width: 16px;
    height: 16px;
    outline: 2px solid #7cc84f !important;
  }

  .tmds-selected-item .tmds-selected-item-icon-check:before {
    content: "\f147";
    font-family: Dashicons;
    text-align: center;
    vertical-align: middle;
  }

  .tmds-variation-image.tmds-selected-item .tmds-selected-item-icon-check:before {
    display: block;
  }

  .tmds-selected-item .tmds-selected-item-icon-check {
    outline: 3px solid #7cc84f;
    background: #ffffff;
  }

  .tmds-product-gallery-item.tmds-selected-item.ui-sortable-handle.tmds-is-product-image,
  .tmds-product-gallery-item.tmds-selected-item.ui-sortable-handle.tmds-is-product-image .tmds-selected-item-icon-check {
    outline: 3px solid #ffbf4a !important;
  }

  i.tmds-set-variation-image {
    width: 20px;
    height: 20px;
    position: absolute;
    left: 20px;
    text-align: center;
    font-size: 16px;
    display: none;
    border-radius: 50%;
    background: white;
    transform: scale3d(1, 1, 1);
    transition: transform 100ms;
  }

  i.tmds-set-product-image.star.icon {
    width: 20px;
    height: 20px;
    position: absolute;
    left: 0;
    text-align: center;
    color: #969696;
    font-size: 16px;
    display: none;
    border-radius: 50%;
    background: white;
    transform: scale3d(1, 1, 1);
    transition: transform 100ms;
  }

  .tmds-allow-set-variation-image .tmds-product-gallery-item:hover i.tmds-set-variation-image {
    display: inline-block;
  }

  .tmds-product-gallery-item:hover i.tmds-set-product-image.star.icon {
    display: inline-block;
  }

  .tmds-product-gallery-item.tmds-is-product-image i.tmds-set-product-image.star.icon {
    color: #ffbf4a;
    display: inline-block;
  }


  i.tmds-set-variation-image:hover {
    color: black;
    transform: scale3d(1.1, 1.1, 1.1);
  }

  i.tmds-set-product-image.star.icon:hover {
    color: #ffbf4a;
    transform: scale3d(1.1, 1.1, 1.1);
  }

  .tmds-import-list {
    margin-top: 0 !important;
    border-top: 0 !important;
    border-radius: 0 0 4px 4px !important;

    .tmds-accordion {
      margin: 30px 0;
      position: relative;

      &.tmds-accordion-removing {
        .button:not(.tmds-button-remove) {
          font-size: 0;
        }
      }

      &.tmds-accordion-importing {
        .button:not(.tmds-button-import) {
          font-size: 0;
        }
      }

      .tmds-product-overlay {
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.5);
        z-index: 2;

        &:before {
          content: url(../images/loading.gif);
          font-size: 15px;
          position: absolute;
          left: 50%;
          top: 50%;
          opacity: 1;
          transform: translate(-50%, -50%) scale3d(0.5, 0.5, 0.5);
          margin: 0;
          padding: 0;
        }
      }

      .title {
        position: relative;

        .tmds-accordion-product-image-title-container {
          display: inline-block;
          width: 100%;
          max-width: calc(100% - 430px);
          vertical-align: middle;

          .tmds-accordion-product-image-title {
            display: flex;
            align-items: center;

            .tmds-accordion-product-image {
              width: 48px;
              outline: 1px solid rgba(1, 1, 1, .1);
              margin: 0 5px;
            }

            .tmds-accordion-product-title-container {
              width: calc(100% - 45px);

              .tmds-accordion-product-title {
                font-weight: 400;
                text-overflow: ellipsis;
                overflow: hidden;
                max-height: 2em;
                white-space: nowrap;
              }

              .tmds-accordion-product-date {
                span {
                  font-weight: 500;
                }
              }

            }
          }
        }

        .tmds-button-view-and-edit {
          position: absolute;
          right: 10px;
          top: 50%;
          transform: translateY(-50%);
        }
      }

      .tmds-fix-width {
        width: 1px !important;
      }

      .tmds-variations-tab .labeled.input input.tmds-import-data-variation-sale-price,
      .tmds-variations-tab .labeled.input input.tmds-import-data-variation-regular-price {
        width: calc(100% - 32px);
        padding: 9px 2px;
        border-top-right-radius: 5px !important;
        border-bottom-right-radius: 5px !important;
      }


      .tmds-sale-price-col, .tmds-regular-price-col {
        width: 12% !important;
      }

      .tmds-inventory-col {
        width: 8% !important;
      }

      .tmds-product-container {
        position: relative;
        /*filter attributes*/
        th.tmds-attribute-filter-list-container:hover .tmds-attribute-filter-list {
          display: block;
        }

        .tmds-table-fix-head {
          overflow-y: auto;
          height: 100px;

          thead td, thead th {
            position: sticky;
            top: -1.2em;
            z-index: 2;
          }

          &.tmds-variation-table-attributes-count-1 {
            min-width: 900px;
          }

          &.tmds-variation-table-attributes-count-2 {
            min-width: 1000px;
          }

          &.tmds-variation-table-attributes-count-3 {
            min-width: 1100px;
          }

          &.tmds-variation-table-attributes-count-4 {
            min-width: 1200px;
          }
        }

        .form-table th {
          padding: 8px;
          vertical-align: middle;
        }

        .vi-ui.table td,
        .form-table td {
          padding: 2px 4px;
        }

        .tmds-attribute-filter-list {
          display: none;
          position: absolute;
          margin: 0;
          text-align: left;
          padding: 4px;
          background: #ffffff;
          color: #000000;
          width: auto;
          right: 0;
          font-weight: 300;
          max-height: 200px;
          overflow: auto;
          min-width: 100%;
          box-shadow: -1px 1px 7px rgba(1, 1, 1, .1);
          max-width: 500px;

          .tmds-attribute-filter-item {
            padding: 4px;
            margin: 1px;
            cursor: pointer;
            transition: all 200ms ease;
            position: relative;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-transform: capitalize;

            &:hover {
              background: #e6e6e6;
              color: black;
            }

            &.tmds-attribute-filter-item-active {
              background: #fff;
              color: black;
              font-weight: 700;
            }
          }
        }

        .tmds-set-price {
          color: #52bae9;
          cursor: pointer;
          text-decoration: underline;
          font-weight: 300;
        }

        .tmds-product-variation-row {
          &.tmds-variation-filter-inactive {
            display: none;
          }

          .tmds-variation-image {
            max-width: 64px;
            height: 64px;
            margin: 5px;
            outline: 1px solid rgba(228, 228, 228, 0.67);
          }
        }
      }
    }
  }
}
.tmds-modal-popup-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99999;

  .tmds-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(1, 1, 1, 0.3);
  }

  .tmds-saving-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.6);
    z-index: 100;
    cursor: progress;

    &:before {
      content: url(../images/loading.gif);
      font-size: 15px;
      position: absolute;
      left: 50%;
      top: 50%;
      opacity: 1;
      transform: translate(-50%, -50%) scale3d(0.5, 0.5, 0.5);
      margin: 0;
      padding: 0;
    }
  }

  .tmds-modal-popup-content {
    position: relative;
    z-index: 99;
    width: 100%;
    max-height: 80%;
    overflow: hidden;
    background: white;
    display: none;
    flex-direction: column;
    max-width: 600px;
    border-radius: 10px;

    .tmds-modal-popup-header {
      display: flex;
      padding: 10px 20px;
      justify-content: flex-start;
      position: relative;

      .tmds-modal-popup-close {
        height: 30px;
        line-height: 30px;
        cursor: pointer;
        position: absolute;
        top: 50%;
        right: 20px;
        transform: translateY(-50%);

        &:before {
          font-family: Dashicons;
          content: "\f335";
          font-size: 30px;
          margin: 0;
          padding: 0;
        }
      }
    }

    .tmds-modal-popup-content-body {
      border-top: 1px solid #eaeaea;
      border-bottom: 1px solid #eaeaea;
      padding-bottom: 20px;
      overflow: auto;

      .tmds-modal-popup-content-body-row {
        display: flex;
        padding-top: 20px;

        .tmds-set-price-action-wrap {
          display: flex;
          flex-direction: column;
          width: 50%;
          padding: 0 20px;
        }

        .tmds-set-price-amount-wrap {
          display: flex;
          flex-direction: column;
          width: 50%;
          padding: 0 20px;
        }

        &.tmds-modal-popup-select-attribute {
          flex-wrap: wrap;
          align-items: center;
          justify-content: center;

          .tmds-attributes-attribute-value {
            margin: 4px;
          }
        }

        .tmds-modal-popup-set-shipping_class-select-wrap,
        .tmds-modal-popup-set-tags-select-wrap,
        .tmds-modal-popup-set-categories-select-wrap {
          width: 100%;
          padding: 0 20px;

          .button {
            margin-top: 10px;
          }
        }
      }
    }

    .tmds-modal-popup-content-footer {
      display: flex;
      padding: 20px;
      justify-content: flex-end;

      .button {
        margin: 0 5px;
      }
    }

    &.tmds-modal-popup-content-remove-attribute .tmds-modal-popup-header {
      padding: 10px 40px;
    }
  }

  &.tmds-modal-popup-container-set-shipping_class .tmds-modal-popup-content-set-shipping_class,
  &.tmds-modal-popup-container-set-tags .tmds-modal-popup-content-set-tags,
  &.tmds-modal-popup-container-set-categories .tmds-modal-popup-content-set-categories,
  &.tmds-modal-popup-container-remove-attribute .tmds-modal-popup-content-remove-attribute,
  &.tmds-modal-popup-container-set-price .tmds-modal-popup-content-set-price {
    display: flex;
  }

  &.tmds-modal-popup-container-set-shipping_class .tmds-modal-popup-content-set-shipping_class,
  &.tmds-modal-popup-container-set-shipping_class .tmds-modal-popup-content-set-shipping_class .tmds-modal-popup-content-body,
  &.tmds-modal-popup-container-set-tags .tmds-modal-popup-content-set-tags,
  &.tmds-modal-popup-container-set-tags .tmds-modal-popup-content-set-tags .tmds-modal-popup-content-body,
  &.tmds-modal-popup-container-set-categories .tmds-modal-popup-content-set-categories,
  &.tmds-modal-popup-container-set-categories .tmds-modal-popup-content-set-categories .tmds-modal-popup-content-body {
    overflow: visible;
  }
}
/*price field*/
.tmds-price-field {
  font-family: Lato, 'Helvetica Neue', Arial, Helvetica, sans-serif;
  margin: 0;
  outline: 0;
  -webkit-appearance: none;
  tap-highlight-color: rgba(255, 255, 255, 0);
  line-height: 1.2142em;
  padding: .67861429em 1em;
  font-size: 1em;
  background: #fff;
  color: rgba(0, 0, 0, .87);
  border-radius: .28571429rem;
  box-shadow: 0 0 0 0 transparent inset;
  -webkit-transition: color .1s ease, border-color .1s ease;
  transition: color .1s ease, border-color .1s ease;
}
/* Setup Wizard*/
#tmds-setup-wizard {
  max-width: 1000px;
  margin: auto;
  font-size: 16px;

  h1 {
    text-align: center;
    line-height: 1.5;
    font-size: 1.7em;
  }

  .tmds-logo {
    text-align: center;
  }

  .tmds-skip-btn {
    text-align: center;
    padding-bottom: 30px;
    a {
      color: #757576;
    }
  }

  .btn-group {
    text-align: right;
    margin-top: 14px;
  }
}
.tmds-plugin-info{
  text-align: justify;
  line-height: 1.5;
  margin-top: 10px;
  font-size: 12px;
}
.vi-ui.attached.tabular.menu {
  flex-wrap: wrap;
}
.tmds-price-rules-wrap{
  margin-top: 30px;
}
.price-rule {
  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    margin-right: 0 !important;
  }
  input[type=number] {
    -moz-appearance: textfield;
  }
  .vi-ui.dropdown .menu .item, input, .vi-ui.label {
    padding: 8px !important;
  }
  tr .vi-ui.dropdown {
    padding-top: 8px !important;
    padding-bottom: 8px !important;
    padding-left: 8px !important;
  }
  th {
    &:first-child {
      width: 33%;
    }
    &.regular-price-rule{
      min-width: 135px;
    }

    &:last-child {
      width: 1%;
    }
  }

  .tmds-price-rule-container .tmds-price-rule-row:first-child:last-child .tmds-price-rule-remove {
    visibility: hidden;
  }
}
.select2-container--default {
  .select2-selection--single {
    height: 35px;
    border: 1px solid rgba(34, 36, 38, 0.15);

    .select2-selection__rendered {
      line-height: 34px;
    }
  }

  .select2-selection--multiple {
    border: 1px solid rgba(34, 36, 38, .15);

    .select2-selection__rendered li .select2-search__field {
      width: 100% !important;
      border: 0;
    }
  }
  .select2-search.select2-search--inline {
    margin-bottom: 0;
  }
}

.tmds-video-guide-wrap {
  margin-top: 20px;
  .content {
    text-align: center;
  }
}
.tmds-save-settings-container{
  position: sticky;
  bottom: 30px;
}
.tmds-logs-wrap {
  #log-viewer {
    background: #fff;
    border: 1px solid #e5e5e5;
    box-shadow: 0 1px 1px #0000000a;
    padding: 5px 20px;
    margin: 10px 0;

    pre {
      font-family: monospace;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
  }

  #log-viewer-select {
    padding: 10px 0 8px;
    line-height: 28px;
  }
}

.tmds-spinner{
  background: #fff !important;
}
.tmds-noscroll {
  overflow-y: hidden !important;

  body {
    position: fixed;
    overflow-y: hidden;
    width: 100%;
  }
}
.thirteen.wide.field > .field {
  margin: 10px 0;
}
.wp-editor-wrap iframe {
  min-height: 330px;
}
.tablenav .tablenav-pages,
.tablenav .subsubsub ul,
.tablenav .subsubsub {
  margin: 0;
}
.wp-admin {

  ::-webkit-scrollbar {
    width: 7px;
    height: 7px;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    margin-top: 2px;
    margin-bottom: 2px;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #cecece;
    border-radius: 20px;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}

@media screen and (max-width: 782px) {
  .vi-ui.styled.accordion .title .tmds-button-view-and-edit {
    transform: unset;
    position: relative;
    right: unset;
    top: unset;
  }

  .tmds-accordion-product-image-title-container {
    max-width: calc(100% - 30px);
  }

  form p.search-box {
    width: 100%;
  }

  form p.search-box input.text.short {
    max-width: 100%;
  }

  .tablenav {
    height: 140px !important;
  }

  .tablenav.top > * {
    padding: 5px;
  }
}

.rtl {
  .tmds-imported-list-wrap .vi-ui.styled.accordion .title .tmds-button-view-and-edit,
  .tmds-import-list-wrap .tmds-import-list .tmds-accordion .title .tmds-button-view-and-edit{
    right: auto;
    left: 10px;
  }
}