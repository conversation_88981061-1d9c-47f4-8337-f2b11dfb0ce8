/*!
 * # Semantic UI 2.1.7 - Checkbox
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Copyright 2015 Contributors
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */
.vi-ui.checkbox{position:relative;display:inline-block;-webkit-backface-visibility:hidden;backface-visibility:hidden;outline:0;vertical-align:baseline;font-style:normal;min-height:17px;font-size:1rem;line-height:17px;min-width:17px}.vi-ui.checkbox input[type=checkbox],.vi-ui.checkbox input[type=radio]{cursor:pointer;position:absolute;top:0;left:0;opacity:0!important;outline:0;z-index:3;width:17px;height:17px}.vi-ui.checkbox .box,.vi-ui.checkbox label{cursor:auto;position:relative;display:block;padding-left:1.85714em;outline:0;font-size:1em}.vi-ui.checkbox .box:after,.vi-ui.checkbox .box:before,.vi-ui.checkbox label:after,.vi-ui.checkbox label:before{position:absolute;top:0;left:0;width:17px;height:17px;-webkit-transition:border .1s ease,opacity .1s ease,-webkit-transform .1s ease,box-shadow .1s ease;transition:border .1s ease,opacity .1s ease,transform .1s ease,box-shadow .1s ease}.vi-ui.checkbox .box:before,.vi-ui.checkbox label:before{content:'';background:#fff;border-radius:.21428571rem;border:1px solid #d4d4d5}.vi-ui.checkbox .box:after,.vi-ui.checkbox label:after{font-size:14px;text-align:center;opacity:0;color:rgba(0,0,0,.87);font-family:Checkbox}.vi-ui.checkbox label,.vi-ui.checkbox+label{color:rgba(0,0,0,.87);-webkit-transition:color .1s ease;transition:color .1s ease}.vi-ui.checkbox+label{vertical-align:middle}.vi-ui.checkbox .box:hover::before,.vi-ui.checkbox label:hover::before{background:#fff;border-color:rgba(34,36,38,.35)}.vi-ui.checkbox label:hover,.vi-ui.checkbox+label:hover,.vi-ui.slider.checkbox .box:hover,.vi-ui.slider.checkbox label:hover{color:rgba(0,0,0,.8)}.vi-ui.checkbox .box:active::before,.vi-ui.checkbox label:active::before{background:#f9fafb;border-color:rgba(34,36,38,.35)}.vi-ui.checkbox .box:active::after,.vi-ui.checkbox input:active~label,.vi-ui.checkbox label:active::after{color:rgba(0,0,0,.95)}.vi-ui.checkbox input:focus~.box:before,.vi-ui.checkbox input:focus~label:before{background:#fff;border-color:#96c8da}.vi-ui.checkbox input:focus~.box:after,.vi-ui.checkbox input:focus~label,.vi-ui.checkbox input:focus~label:after{color:rgba(0,0,0,.95)}.vi-ui.checkbox input:checked~.box:before,.vi-ui.checkbox input:checked~label:before{background:#fff;border-color:rgba(34,36,38,.35)}.vi-ui.checkbox input:checked~.box:after,.vi-ui.checkbox input:checked~label:after{opacity:1;color:rgba(0,0,0,.95)}.vi-ui.checkbox input:indeterminate~.box:before,.vi-ui.checkbox input:indeterminate~label:before{background:#fff;border-color:rgba(34,36,38,.35)}.vi-ui.checkbox input:indeterminate~.box:after,.vi-ui.checkbox input:indeterminate~label:after{opacity:1;color:rgba(0,0,0,.95)}.vi-ui.checkbox input:checked:focus~.box:before,.vi-ui.checkbox input:checked:focus~label:before,.vi-ui.checkbox input:indeterminate:focus~.box:before,.vi-ui.checkbox input:indeterminate:focus~label:before{background:#fff;border-color:#96c8da}.vi-ui.checkbox input:checked:focus~.box:after,.vi-ui.checkbox input:checked:focus~label:after,.vi-ui.checkbox input:indeterminate:focus~.box:after,.vi-ui.checkbox input:indeterminate:focus~label:after{color:rgba(0,0,0,.95)}.vi-ui.read-only.checkbox,.vi-ui.read-only.checkbox label{cursor:default}.vi-ui.checkbox input[disabled]~.box:after,.vi-ui.checkbox input[disabled]~label,.vi-ui.disabled.checkbox .box:after,.vi-ui.disabled.checkbox label{cursor:default;opacity:.5;color:#000}.vi-ui.checkbox input.hidden{z-index:-1}.vi-ui.checkbox input.hidden+label{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.vi-ui.radio.checkbox{min-height:15px}.vi-ui.radio.checkbox .box,.vi-ui.radio.checkbox label{padding-left:1.85714em}.vi-ui.radio.checkbox .box:before,.vi-ui.radio.checkbox label:before{content:'';-webkit-transform:none;-ms-transform:none;transform:none;width:15px;height:15px;border-radius:500rem;top:1px;left:0}.vi-ui.radio.checkbox .box:after,.vi-ui.radio.checkbox label:after{border:0;content:''!important;line-height:15px;top:1px;left:0;width:15px;height:15px;border-radius:500rem;-webkit-transform:scale(.46666667);-ms-transform:scale(.46666667);transform:scale(.46666667);background-color:rgba(0,0,0,.87)}.vi-ui.radio.checkbox input:focus~.box:before,.vi-ui.radio.checkbox input:focus~label:before{background-color:#fff}.vi-ui.radio.checkbox input:focus~.box:after,.vi-ui.radio.checkbox input:focus~label:after{background-color:rgba(0,0,0,.95)}.vi-ui.radio.checkbox input:indeterminate~.box:after,.vi-ui.radio.checkbox input:indeterminate~label:after{opacity:0}.vi-ui.radio.checkbox input:checked~.box:before,.vi-ui.radio.checkbox input:checked~label:before{background-color:#fff}.vi-ui.radio.checkbox input:checked~.box:after,.vi-ui.radio.checkbox input:checked~label:after{background-color:rgba(0,0,0,.95)}.vi-ui.radio.checkbox input:focus:checked~.box:before,.vi-ui.radio.checkbox input:focus:checked~label:before{background-color:#fff}.vi-ui.radio.checkbox input:focus:checked~.box:after,.vi-ui.radio.checkbox input:focus:checked~label:after{background-color:rgba(0,0,0,.95)}.vi-ui.slider.checkbox{min-height:1.25rem}.vi-ui.slider.checkbox input{width:3.5rem;height:1.25rem}.vi-ui.slider.checkbox .box,.vi-ui.slider.checkbox label{padding-left:4.5rem;line-height:1rem;color:rgba(0,0,0,.4)}.vi-ui.slider.checkbox .box:before,.vi-ui.slider.checkbox label:before{display:block;position:absolute;content:'';border:0!important;left:0;z-index:1;top:.4rem;background-color:rgba(0,0,0,.05);width:3.5rem;height:.21428571rem;-webkit-transform:none;-ms-transform:none;transform:none;border-radius:500rem;-webkit-transition:background .3s ease;transition:background .3s ease}.vi-ui.slider.checkbox .box:after,.vi-ui.slider.checkbox label:after{background:-webkit-linear-gradient(transparent,rgba(0,0,0,.05)) #fff;background:linear-gradient(transparent,rgba(0,0,0,.05)) #fff;position:absolute;content:''!important;opacity:1;z-index:2;border:0;box-shadow:0 1px 2px 0 rgba(34,36,38,.15),0 0 0 1px rgba(34,36,38,.15) inset;width:1.5rem;height:1.5rem;top:-.25rem;left:0;-webkit-transform:none;-ms-transform:none;transform:none;border-radius:500rem;-webkit-transition:left .3s ease;transition:left .3s ease}.vi-ui.slider.checkbox input:focus~.box:before,.vi-ui.slider.checkbox input:focus~label:before{background-color:rgba(0,0,0,.15);border:0}.vi-ui.slider.checkbox .box:hover::before,.vi-ui.slider.checkbox label:hover::before{background:rgba(0,0,0,.15)}.vi-ui.slider.checkbox input:checked~.box,.vi-ui.slider.checkbox input:checked~label{color:rgba(0,0,0,.95)!important}.vi-ui.slider.checkbox input:checked~.box:before,.vi-ui.slider.checkbox input:checked~label:before{background-color:#545454!important}.vi-ui.slider.checkbox input:checked~.box:after,.vi-ui.slider.checkbox input:checked~label:after{left:2rem}.vi-ui.slider.checkbox input:focus:checked~.box,.vi-ui.slider.checkbox input:focus:checked~label,.vi-ui.toggle.checkbox input:focus:checked~.box,.vi-ui.toggle.checkbox input:focus:checked~label{color:rgba(0,0,0,.95)!important}.vi-ui.slider.checkbox input:focus:checked~.box:before,.vi-ui.slider.checkbox input:focus:checked~label:before{background-color:#000!important}.vi-ui.toggle.checkbox{min-height:1.5rem}.vi-ui.toggle.checkbox input{width:3.5rem;height:1.5rem}.vi-ui.toggle.checkbox .box,.vi-ui.toggle.checkbox label{min-height:1.5rem;padding-left:4.5rem;color:rgba(0,0,0,.87)}.vi-ui.toggle.checkbox label{padding-top:.15em}.vi-ui.toggle.checkbox .box:before,.vi-ui.toggle.checkbox label:before{display:block;position:absolute;content:'';z-index:1;-webkit-transform:none;-ms-transform:none;transform:none;border:0;top:0;background:rgba(0,0,0,.05);width:3.5rem;height:1.5rem;border-radius:500rem}.vi-ui.toggle.checkbox .box:after,.vi-ui.toggle.checkbox label:after{background:-webkit-linear-gradient(transparent,rgba(0,0,0,.05)) #fff;background:linear-gradient(transparent,rgba(0,0,0,.05)) #fff;position:absolute;content:''!important;opacity:1;z-index:2;border:0;box-shadow:0 1px 2px 0 rgba(34,36,38,.15),0 0 0 1px rgba(34,36,38,.15) inset;width:1.5rem;height:1.5rem;top:0;left:0;border-radius:500rem;-webkit-transition:background .3s ease,left .3s ease;transition:background .3s ease,left .3s ease}.vi-ui.toggle.checkbox input~.box:after,.vi-ui.toggle.checkbox input~label:after{left:-.05rem}.vi-ui.toggle.checkbox .box:hover::before,.vi-ui.toggle.checkbox input:focus~.box:before,.vi-ui.toggle.checkbox input:focus~label:before,.vi-ui.toggle.checkbox label:hover::before{background-color:rgba(0,0,0,.15);border:0}.vi-ui.toggle.checkbox input:checked~.box,.vi-ui.toggle.checkbox input:checked~label{color:rgba(0,0,0,.95)!important}.vi-ui.toggle.checkbox input:checked~.box:before,.vi-ui.toggle.checkbox input:checked~label:before{background-color:#2185d0!important}.vi-ui.toggle.checkbox input:checked~.box:after,.vi-ui.toggle.checkbox input:checked~label:after{left:2.15rem}.vi-ui.toggle.checkbox input:focus:checked~.box:before,.vi-ui.toggle.checkbox input:focus:checked~label:before{background-color:#0d71bb!important}.vi-ui.fitted.checkbox .box,.vi-ui.fitted.checkbox label{padding-left:0!important}.vi-ui.fitted.slider.checkbox,.vi-ui.fitted.toggle.checkbox{width:3.5rem}@font-face{font-family:Checkbox;src:url(data:application/x-font-ttf;charset=utf-8;base64,AAEAAAALAIAAAwAwT1MvMg8SBD8AAAC8AAAAYGNtYXAYVtCJAAABHAAAAFRnYXNwAAAAEAAAAXAAAAAIZ2x5Zn4huwUAAAF4AAABYGhlYWQGPe1ZAAAC2AAAADZoaGVhB30DyAAAAxAAAAAkaG10eBBKAEUAAAM0AAAAHGxvY2EAmgESAAADUAAAABBtYXhwAAkALwAAA2AAAAAgbmFtZSC8IugAAAOAAAABknBvc3QAAwAAAAAFFAAAACAAAwMTAZAABQAAApkCzAAAAI8CmQLMAAAB6wAzAQkAAAAAAAAAAAAAAAAAAAABEAAAAAAAAAAAAAAAAAAAAABAAADoAgPA/8AAQAPAAEAAAAABAAAAAAAAAAAAAAAgAAAAAAADAAAAAwAAABwAAQADAAAAHAADAAEAAAAcAAQAOAAAAAoACAACAAIAAQAg6AL//f//AAAAAAAg6AD//f//AAH/4xgEAAMAAQAAAAAAAAAAAAAAAQAB//8ADwABAAAAAAAAAAAAAgAANzkBAAAAAAEAAAAAAAAAAAACAAA3OQEAAAAAAQAAAAAAAAAAAAIAADc5AQAAAAABAEUAUQO7AvgAGgAAARQHAQYjIicBJjU0PwE2MzIfAQE2MzIfARYVA7sQ/hQQFhcQ/uMQEE4QFxcQqAF2EBcXEE4QAnMWEP4UEBABHRAXFhBOEBCoAXcQEE4QFwAAAAABAAABbgMlAkkAFAAAARUUBwYjISInJj0BNDc2MyEyFxYVAyUQEBf9SRcQEBAQFwK3FxAQAhJtFxAQEBAXbRcQEBAQFwAAAAABAAAASQMlA24ALAAAARUUBwYrARUUBwYrASInJj0BIyInJj0BNDc2OwE1NDc2OwEyFxYdATMyFxYVAyUQEBfuEBAXbhYQEO4XEBAQEBfuEBAWbhcQEO4XEBACEm0XEBDuFxAQEBAX7hAQF20XEBDuFxAQEBAX7hAQFwAAAQAAAAIAAHRSzT9fDzz1AAsEAAAAAADRsdR3AAAAANGx1HcAAAAAA7sDbgAAAAgAAgAAAAAAAAABAAADwP/AAAAEAAAAAAADuwABAAAAAAAAAAAAAAAAAAAABwQAAAAAAAAAAAAAAAIAAAAEAABFAyUAAAMlAAAAAAAAAAoAFAAeAE4AcgCwAAEAAAAHAC0AAQAAAAAAAgAAAAAAAAAAAAAAAAAAAAAAAAAOAK4AAQAAAAAAAQAIAAAAAQAAAAAAAgAHAGkAAQAAAAAAAwAIADkAAQAAAAAABAAIAH4AAQAAAAAABQALABgAAQAAAAAABgAIAFEAAQAAAAAACgAaAJYAAwABBAkAAQAQAAgAAwABBAkAAgAOAHAAAwABBAkAAwAQAEEAAwABBAkABAAQAIYAAwABBAkABQAWACMAAwABBAkABgAQAFkAAwABBAkACgA0ALBDaGVja2JveABDAGgAZQBjAGsAYgBvAHhWZXJzaW9uIDIuMABWAGUAcgBzAGkAbwBuACAAMgAuADBDaGVja2JveABDAGgAZQBjAGsAYgBvAHhDaGVja2JveABDAGgAZQBjAGsAYgBvAHhSZWd1bGFyAFIAZQBnAHUAbABhAHJDaGVja2JveABDAGgAZQBjAGsAYgBvAHhGb250IGdlbmVyYXRlZCBieSBJY29Nb29uLgBGAG8AbgB0ACAAZwBlAG4AZQByAGEAdABlAGQAIABiAHkAIABJAGMAbwBNAG8AbwBuAC4AAAADAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA) format('truetype')}.vi-ui.checkbox input:checked~.box:after,.vi-ui.checkbox input:checked~label:after{content:'\e800'}.vi-ui.checkbox input:indeterminate~.box:after,.vi-ui.checkbox input:indeterminate~label:after{font-size:12px;content:'\e801'}