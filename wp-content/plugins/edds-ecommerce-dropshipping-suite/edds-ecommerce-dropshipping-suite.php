<?php
/**
 * Plugin Name: EDDS - E-commerce Dropshipping Suite
 * Plugin URI: https://shakilahamed.com/edds-ecommerce-dropshipping-suite/
 * Description: Effortlessly transfer product data from multiple e-commerce platforms to WooCommerce with ease
 * Version: 1.0.4
 * Author: Shakil Ahamed
 * Author URI: https://shakilahamed.com
 * License:  GPL v2 or later
 * License URI:  https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: edds-ecommerce-dropshipping-suite
 * Domain Path: /languages
 * Copyright 2025 Shakil Ahamed. All rights reserved.
 * Requires Plugins: woocommerce
 * Requires PHP: 7.0
 * Requires at least: 6.2
 * Tested up to: 6.8
 * WC tested up to: 10.0
 **/
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}
if ( ! defined( 'EDDS_VERSION' ) ) {
	define( 'EDDS_VERSION', '1.0.4' );
	define( 'EDDS_NAME', 'EDDS - E-commerce Dropshipping Suite' );
	define( 'EDDS_BASENAME', plugin_basename( __FILE__ ) );
	define( 'EDDS_DIR', plugin_dir_path( __FILE__ ) );
	define( 'EDDS_LANGUAGES', EDDS_DIR . "languages" . DIRECTORY_SEPARATOR );
	define( 'EDDS_INCLUDES', EDDS_DIR . "includes" . DIRECTORY_SEPARATOR );
	define( 'EDDS_ADMIN', EDDS_DIR . "admin" . DIRECTORY_SEPARATOR );
	define( 'EDDS_FRONTEND', EDDS_DIR . "frontend" . DIRECTORY_SEPARATOR );
	define( 'EDDS_TEMPLATES', EDDS_DIR . "templates" . DIRECTORY_SEPARATOR );
	define( 'EDDS_PLUGINS', EDDS_DIR . "plugins" . DIRECTORY_SEPARATOR );
	$plugin_url = plugins_url( 'assets/', __FILE__ );
	define( 'EDDS_CSS', $plugin_url . "css/" );
	define( 'EDDS_JS', $plugin_url . "js/" );
	define( 'EDDS_IMAGES', $plugin_url . "images/" );
	define( 'EDDS_Admin_Class_Prefix', "EDDS_Admin_" );
	define( 'EDDS_Frontend_Class_Prefix', "EDDS_Frontend_" );
}

if ( ! class_exists( 'EDDS' ) ) {
	class VIEDDS_Init {
		public function __construct() {
			//Compatible with High-Performance order storage (COT)
			add_action( 'before_woocommerce_init', array( $this, 'before_woocommerce_init' ) );
			add_action( 'activated_plugin', array( $this, 'after_activated' ), 10, 2 );
			add_action( 'plugins_loaded', array( $this, 'check_environment' ) );
		}
        public function has_pro(){
            return defined('EDDSPRO_VERSION');
        }
		public function after_activated( $plugin, $network_wide ) {
			if ( $plugin !== EDDS_BASENAME || $this->has_pro()) {
				return;
			}
			global $wpdb;
			if ( function_exists( 'is_multisite' ) && is_multisite() && $network_wide ) {
				$current_blog = $wpdb->blogid;
				$blogs        = $wpdb->get_col( $wpdb->prepare('SELECT blog_id FROM %i',[$wpdb->blogs]) );// phpcs:ignore WordPress.DB.DirectDatabaseQuery.DirectQuery, WordPress.DB.DirectDatabaseQuery.NoCaching

				//Multi site activate action
				foreach ( $blogs as $blog ) {
					switch_to_blog( $blog );
					$this->create_table();
				}
				switch_to_blog( $current_blog );
			} else {
				//Single site activate action
				$this->create_table();
			}
			if ( ! get_option( 'edds_params' ) ) {
				update_option( 'edds_setup_wizard', 1, 'no' );
				$this->check_environment(  );
			}
		}
		public function create_table() {
			if ( ! class_exists( 'EDDS_Error_Images_Table' ) ) {
				require_once EDDS_INCLUDES . "class" . DIRECTORY_SEPARATOR . 'error-images-table.php';
			}
			if ( ! class_exists( 'EDDS_Products_Table' ) ) {
				require_once EDDS_INCLUDES . "class" . DIRECTORY_SEPARATOR . 'edds-products-table.php';
			}
			EDDS_Products_Table::maybe_create_table();
			EDDS_Error_Images_Table::create_table();
		}
		public function check_environment($recent_activate = false ) {
            if ($this->has_pro()){
                return;
            }
			if ( ! class_exists( 'VillaTheme_Require_Environment' ) ) {
				require_once EDDS_INCLUDES . 'support.php';
			}
			$environment = new \VillaTheme_Require_Environment( [
				'plugin_name'     => EDDS_NAME,
				'php_version'     => '7.0',
				'wp_version'       => '6.2',
				'require_plugins' => [
					[
						'slug' => 'woocommerce',
						'name' => 'WooCommerce',
						'defined_version' => 'WC_VERSION',
						'version' => '7.0',
					],
				],
			] );
			if ( $environment->has_error() ) {
				return;
			}
			if ( get_option( 'edds_setup_wizard' ) &&
			     ( ! empty( $_GET['page'] ) && strpos( sanitize_text_field( wp_unslash( $_GET['page'] ) ), "edds" ) === 0 )  ) {// phpcs:ignore WordPress.Security.NonceVerification.Recommended
				$url = add_query_arg( [
					'edds_setup_wizard' => true,
					'_wpnonce'                      => wp_create_nonce( 'edds_setup' )
				], admin_url() );
				wp_safe_redirect( $url );
				exit();
			}
			global $wpdb;
			$tables = array(
				'edds_posts'    => 'edds_posts',
				'edds_postmeta' => 'edds_postmeta'
			);
			foreach ( $tables as $name => $table ) {
				$wpdb->$name    = $wpdb->prefix . $table;
				$wpdb->tables[] = $table;
			}
			add_action( 'admin_notices', array( $this, 'admin_notices' ) );
			$this->includes();
			add_action( 'init', array( $this, 'init' ) );
			add_filter( 'plugin_action_links_' . EDDS_BASENAME, array( $this, 'settings_link' ) );
		}
		public function admin_notices() {
			$errors              = [];
			$permalink_structure = get_option( 'permalink_structure' );
			if ( ! $permalink_structure ) {
				$errors[] = sprintf( "%s <a href='%s' target='_blank'>%s</a> %s",
					esc_html__( 'You are using Permalink structure as Plain. Please go to', 'edds-ecommerce-dropshipping-suite' ),
					esc_html( admin_url( 'options-permalink.php' ) ),
					esc_html__( 'Permalink Settings', 'edds-ecommerce-dropshipping-suite'),
					esc_html__( 'to change it', 'edds-ecommerce-dropshipping-suite')
				);
			}

			if ( ! is_ssl() ) {
				$errors[] = sprintf( "%s <a href='https://wordpress.org/documentation/article/https-for-wordpress/' target='_blank'>%s</a>",
					esc_html__( 'Your site is not using HTTPS. For more details, please read', 'edds-ecommerce-dropshipping-suite'),
					esc_html__( 'HTTPS for WordPress', 'edds-ecommerce-dropshipping-suite' )
				);
			}
			if ( ! empty( $errors ) ) {
				?>
				<div class="error">
					<h3>
						<?php
						echo esc_html( EDDS_NAME ) . ': ' . esc_html( _n( 'you can not import products unless below issue is resolved',
								'you can not import products unless below issues are resolved',
								count( $errors ), 'edds-ecommerce-dropshipping-suite' ) );
						?>
					</h3>
					<?php
					foreach ( $errors as $error ) {
						echo wp_kses_post( "<p>{$error}</p>" );
					}
					?>
				</div>
				<?php
			}
		}
		protected function includes() {
			$files = array(
				EDDS_INCLUDES=>[
					'file_name' => [
						'support.php',
						'data.php',
						'background-process/wp-async-request.php',
						'background-process/wp-background-process.php',
						'class/error-images-table.php',
						'class/download-images.php',
						'class/edds-post.php',
						'class/edds-post-query.php',
						'class/edds-products-table.php',
					]
				],
				EDDS_ADMIN=>[
					'class_prefix' => EDDS_Admin_Class_Prefix,
					'file_name' => [
						'api.php',
						'auth.php',
						'log.php',
						'error-images.php',
						'import-list.php',
						'imported.php',
						'product.php',
						'settings.php',
						'setup-wizard.php',
						'recommend.php',
					]
				],
			);
			foreach ( $files as $path => $items ) {
				if (empty($items['file_name']) || !is_array($items['file_name'])){
					continue;
				}
				$class_prefix = $items['class_prefix']??'';
				foreach ($items['file_name'] as $file_name){
					$file = $path.'/'.$file_name;
					if ( !file_exists( $file ) ) {
						continue;
					}
					require_once $file;
					$ext_file  = pathinfo( $file);
					$class_name = $ext_file['filename'] ??'';
					if ($class_prefix){
						$class_name = preg_replace( '/\W/i', '_', $class_prefix . ucfirst( $class_name ) );
					}
					if ( $class_name && class_exists( $class_name ) ) {
						new $class_name;
					}
				}
			}
		}
		public function init() {
			$this->load_plugin_textdomain();
			if ( class_exists( 'VillaTheme_Support' ) ) {
				new VillaTheme_Support(
					array(
						'support'    => 'https://wordpress.org/support/plugin/edds-ecommerce-dropshipping-suite/',
						'docs'       => 'https://shakilahamed.com/docs/edds',
						'review'     => 'https://wordpress.org/support/plugin/edds-ecommerce-dropshipping-suite/reviews/?rate=5#rate-response',
						'pro_url'    => '',
						'css'        => EDDS_CSS,
						'image'      => EDDS_IMAGES,
						'slug'       => 'edds-ecommerce-dropshipping-suite',
						'menu_slug'  => 'edds',
						'version'    => EDDS_VERSION,
						'survey_url' => '',
					)
				);
			}
		}
		public function load_plugin_textdomain() {
			/**
			 * load Language translate
			 */
			$locale = apply_filters( 'plugin_locale', get_locale(), 'edds-ecommerce-dropshipping-suite' );
			load_textdomain( 'edds-ecommerce-dropshipping-suite', EDDS_LANGUAGES . "ecommerce-dropshipping-suite-$locale.mo" );
		}
		public function settings_link( $links ) {
			$settings_link = sprintf( '<a href="%s" title="%s">%s</a>', esc_attr( admin_url( 'admin.php?page=edds' ) ),
				esc_attr__( 'Settings', 'edds-ecommerce-dropshipping-suite' ),
				esc_html__( 'Settings', 'edds-ecommerce-dropshipping-suite'  )
			);
			array_unshift( $links, $settings_link );
			return $links;
		}
		public function before_woocommerce_init() {
			if ( class_exists( '\Automattic\WooCommerce\Utilities\FeaturesUtil' ) ) {
				\Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility( 'custom_order_tables', __FILE__, true );
			}
		}
	}
	new VIEDDS_Init();
}