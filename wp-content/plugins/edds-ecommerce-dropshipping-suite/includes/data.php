<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

class EDDS_DATA {
	public static $prefix = 'edds';
	private $params, $default, $current_params;
	protected static $instance = null;
	protected static $allow_html;
	public function __construct() {
		global $edds_params;
		$this->default        = array(
			'enable'                      => 1,
			'product_status'              => 'publish',
			'catalog_visibility'          => 'visible',
			'product_categories'          => [],
			'product_tags'                => [],
			'variation_visible'           => '',
			'manage_stock'                => 1,
			'import_currency_rate'        => [],
			'price_from'                  => [ 0 ],
			'price_default'               => [
				'plus_value'      => 2,
				'plus_sale_value' => 1,
				'plus_value_type' => 'multiply',
			],
			'price_to'                    => [ '' ],
			'plus_value'                  => [ 200 ],
			'plus_sale_value'             => [ - 1 ],
			'plus_value_type'             => [ 'percent' ],
			'use_global_attributes'       => 1,
			'simple_if_one_variation'     => '',
			'product_description'         => 'none',
			'use_external_image'          => '',
			'download_description_images' => '',
			'product_gallery'             => 1,
			'disable_background_process'  => '',
			'product_shipping_class'      => '',
		);
		if ( ! $edds_params ) {
			$edds_params = get_option( 'edds_params', array() );
		}
		$this->current_params = $edds_params;
		$this->params         = wp_parse_args( $edds_params, $this->default ) ;
	}

	public static function get_instance( $new = false ) {
		if ( $new || null === self::$instance ) {
			self::$instance = new self;
		}

		return self::$instance;
	}
	public function get_params( $name = "") {
		if ( ! $name ) {
			return apply_filters( 'villatheme_'.self::$prefix.'_params', $this->params);
		}
		$name_t = $name ;
		switch ($name_t){
			case 'import_currency_rate':
				if (isset($this->current_params[$name_t]) || !empty($this->params[$name_t])){
					break;
				}
				$current_currency = get_woocommerce_currency();
				$import_currency_rate = [];
				$accept_currency = self::get_temu_currencies();
				if (empty($import_currency_rate)){
					if (isset($accept_currency[$current_currency])){
						$import_currency_rate[$current_currency] = 1;
					}else{
						$import_currency_rate['USD'] =1;
					}
				}
				$this->params[$name_t] = $import_currency_rate;
				break;
			case 'product_categories':
				if (isset($this->current_params[$name_t]) || !empty($this->params[$name_t])){
					break;
				}
				$this->params[$name_t] = array( get_option( 'default_product_cat', 0 ) );
				break;
		}
		return apply_filters( 'villatheme_'.self::$prefix.'_params_' . $name_t, $this->params[ $name_t ] ?? $this->params[ $name ] ?? false );
	}

	public function get_default( $name = "" ) {
		if ( ! $name ) {
			return $this->default;
		} elseif ( isset( $this->default[ $name ] ) ) {
			return apply_filters( 'villatheme_'.self::$prefix.'_params_default-' . $name, $this->default[ $name ] );
		} else {
			return false;
		}
	}
	public static function get_temu_pd_id($woo_pd_id ) {
		$product = wc_get_product($woo_pd_id);
		if ($product){
			$product_id = $product->get_meta('_' . self::$prefix . '_product_id');
		}
		return $product_id ?? '';
	}
	public static function get_temu_pd_url($pd_id , $is_woo_id = false) {
		if ($is_woo_id){
			$prefix = self::$prefix;
			$args    = array(
				'edds_query'          => 1,
				'post_type'      => $prefix . '_draft_product',
				'order'          => 'DESC',
				'post_status'          => ['publish', 'draft', 'override','trash'],
				'fields'         => 'ids',
				'posts_per_page' => 1,
				'meta_query' => [// phpcs:ignore WordPress.DB.SlowDBQuery.slow_db_query_meta_query
					'relation' => 'and',
					[
						'key'     => '_' . $prefix . '_woo_id',
						'compare' => '=',
						'value' => $pd_id,
					]
				],
			);
			$the_query   = EDDS_Post::query( $args );
			$ids = $the_query->get_posts();
			wp_reset_postdata();
			$pd_id = $ids[0] ?? '';
		}
		return self::get_temu_url($pd_id);
	}
	public static function get_temu_url($edds_id ='', $path='') {
        if ($edds_id){
            $path = EDDS_Post::get_post_meta($edds_id,'_'.(self::$prefix).'_url',true);
        }
        if ($path){
            $url = 'https://www.temu.com'.$path;
        }
        return $url ?? '';
	}
	public static function get_temu_currencies() {
		return self::json_decode('{"AED":"AED : AED","ALL":"ALL : L","AMD":"AMD : \u058f","AUD":"AUD : AU$","AZN":"AZN : \u20bc","BAM":"BAM : KM","BGN":"BGN : \u043b\u0432.","BHD":"BHD : BHD","BND":"BND : BND","BRL":"BRL : R$","CAD":"CAD : CA$","CHF":"CHF : CHF","CZK":"CZK : K\u010d","DKK":"DKK : kr.","DOP":"DOP : RD$","EUR":"EUR : \u20ac","GBP":"GBP : \u00a3","GEL":"GEL : \u20be","GTQ":"GTQ : Q","HUF":"HUF : Ft","ILS":"ILS : \u20aa","ISK":"ISK : kr;NOK : kr;SEK : kr","JOD":"JOD : JOD","JPY":"JPY : \u5186","KRW":"KRW : \u20a9","KWD":"KWD : KWD","KZT":"KZT : \u20b8","MAD":"MAD : DH","MDL":"MDL : lei","MKD":"MKD : \u0434\u0435\u043d","MUR":"MUR : Rs","MXN":"MXN : MX$","MYR":"MYR : RM","NGN":"NGN : \u20a6","NZD":"NZD : NZ$","OMR":"OMR : OMR","PAB":"PAB : B\/.","PEN":"PEN : S\/","PHP":"PHP : \u20b1","PKR":"PKR : Rs.","PLN":"PLN : z\u0142","QAR":"QAR : QAR","RON":"RON : Lei","RSD":"RSD : RSD","SAR":"SAR : SAR","THB":"THB : \u0e3f","TRY":"TRY : TL","TTD":"TTD : TT$","UAH":"UAH : \u20b4","USD":"USD : USD;USD : $;CLP : $;COP : $","UYU":"UYU : $","UZS":"UZS : so\'m","VND":"VND : \u20ab","ZAR":"ZAR : R"}');
	}
	public static function get_price_with_temu_decimals($price,$currency) {
		$decimals = self::json_decode('{"VND":0,"USD":2,"EUR":2,"AUD":2,"BGN":2,"CAD":2,"CZK":0,"DKK":2,"HUF":0,"ISK":0,"JPY":0,"MYR":2,"MXN":2,"NZD":2,"NOK":0,"PLN":2,"KRW":0,"RON":2,"SEK":2,"CHF":2,"TRY":2,"GBP":2}');
        $decimal = 2;
        if (isset($decimals[$currency])){
            $decimal = intval($decimals[$currency]);
        }
        $check = 0;
        if ($decimal){
            $check = 1;
            for ($i = 0; $i<$decimal; $i++){
                $check *=10;
            }
        }
        return $check? $price/$check : $price;
    }

	public static function chrome_extension_buttons() {
		?>
        <span class="vi-ui positive button labeled icon edds-connect-chrome-extension edds-hidden"
              data-site_url="<?php echo esc_url( site_url() ) ?>"><i
                    class="linkify icon"></i><?php esc_html_e( 'Connect the Extension', 'edds-ecommerce-dropshipping-suite' ) ?></span>
        <a target="_blank" href="https://www.temu.com"
           class="vi-ui primary button labeled icon edds-import-products edds-hidden">
            <i class="external icon"></i>
			<?php esc_html_e( 'Go to E-commerce Products', 'edds-ecommerce-dropshipping-suite' ) ?>
        </a>
        <a target="_blank" href="https://shakilahamed.com/downloads/edds-extension"
           class="vi-ui positive button labeled icon edds-download-chrome-extension">
            <i class="external icon"></i>
			<?php esc_html_e( 'Install Chrome Extension', 'edds-ecommerce-dropshipping-suite' ) ?>
        </a>
		<?php
	}
	/**
	 * @param $tags
	 *
	 * @return array
	 */
	public static function filter_allowed_html( $tags = [] ) {
		if ( self::$allow_html && empty( $tags ) ) {
			return self::$allow_html;
		}
		$tags = array_merge_recursive( $tags, wp_kses_allowed_html( 'post' ), array(
			'input'  => array(
				'type'         => 1,
				'id'           => 1,
				'name'         => 1,
				'class'        => 1,
				'placeholder'  => 1,
				'autocomplete' => 1,
				'style'        => 1,
				'value'        => 1,
				'size'         => 1,
				'checked'      => 1,
				'disabled'     => 1,
				'readonly'     => 1,
				'data-*'       => 1,
			),
			'form'   => array(
				'method' => 1,
				'action' => 1,
			),
			'select' => array(
				'name'     => 1,
				'multiple' => 1,
			),
			'option' => array(
				'value'    => 1,
				'selected' => 1,
				'disabled'     => 1,
			),
			'style'  => array(
				'id'    => 1,
				'class' => 1,
				'type'  => 1,
			),
			'source' => array(
				'type' => 1,
				'src'  => 1
			),
			'video'  => array(
				'width'  => 1,
				'height' => 1,
				'src'    => 1
			),
			'iframe' => array(
				'width'           => 1,
				'height'          => 1,
				'allowfullscreen' => 1,
				'allow'           => 1,
				'src'             => 1
			),
		) );
		$tmp = $tags;
		foreach ( $tmp as $key => $value ) {
			if ( in_array( $key, array( 'div', 'span', 'a', 'form', 'select', 'option', 'table', 'tr', 'th', 'td' ) ) ) {
				$tags[ $key ] = wp_parse_args( [
					'width'  => 1,
					'height' => 1,
					'class'  => 1,
					'id'     => 1,
					'type'   => 1,
					'style'  => 1,
					'data-*' => 1,
				],$value);
			}
		}
		self::$allow_html = $tags;
		return self::$allow_html;
	}
	public static function implode_html_attributes( $raw_attributes ) {
		$attributes = array();
		foreach ( $raw_attributes as $name => $value ) {
			$attributes[] = esc_attr( $name ) . '="' . esc_attr( $value ) . '"';
		}
		return implode( ' ', $attributes );
	}
	public static function villatheme_render_field( $name, $field ) {
		if ( ! $name ) {
			return;
		}
		if ( ! empty( $field['html'] ) ) {
			echo wp_kses($field['html'], self::filter_allowed_html());
			return;
		}
		$type  = $field['type'] ?? '';
		$value = $field['value'] ?? '';
		if ( ! empty( $field['prefix'] ) ) {
			$id = "edds-{$field['prefix']}-{$name}";
		} else {
			$id = "edds-{$name}";
		}
		$class             = $field['class'] ?? $id;
		$custom_attributes = array_merge( [
			'type'  => $type,
			'name'  => $name,
			'id'    => $id,
			'value' => $value,
			'class' => $class,
		], (array) ( $field['custom_attributes'] ?? [] ) );
		if ( ! empty( $field['input_label'] ) ) {
			$input_label_type = $field['input_label']['type'] ?? 'left';
			echo wp_kses( sprintf( '<div class="vi-ui %s labeled input">', ( ! empty( $field['input_label']['fluid'] ) ? 'fluid ' : '' ) . $input_label_type ), self::filter_allowed_html() );
			if ( $input_label_type === 'left' ) {
				echo wp_kses( sprintf( '<div class="%s">%s</div>', $field['input_label']['label_class'] ?? 'vi-ui label', $field['input_label']['label'] ?? '' ), self::filter_allowed_html() );
			}
		}
		if (!empty($field['empty_name_field'])){
			unset($custom_attributes['name']);
		}
		switch ( $type ) {
			case 'premium_option':
				printf('<a class="vi-ui button" href="premium_option_url"
                                       target="_blank">%s</a>', esc_html__( 'Unlock This Feature', 'edds-ecommerce-dropshipping-suite' ));
				break;
			case 'checkbox':
				unset( $custom_attributes['type'] );
				echo wp_kses( sprintf( '
					<div class="vi-ui toggle checkbox%s">
						<input type="hidden" %s>
						<input type="checkbox" id="%s-checkbox" %s ><label></label>
					</div>', !empty($field['disabled']) ? ' disabled' : '',self::implode_html_attributes( $custom_attributes ),  $id,  $value ? 'checked' : ''
				), self::filter_allowed_html() );
				break;
			case 'select':
				$select_options = $field['options'] ?? '';
				$multiple       = $field['multiple'] ?? '';
				unset( $custom_attributes['type'] );
				unset( $custom_attributes['value'] );
				$custom_attributes['class'] = "vi-ui fluid dropdown {$class}";
				if ( $multiple ) {
					$value                         = (array) $value;
					$custom_attributes['name']     = $name . '[]';
					$custom_attributes['multiple'] = "multiple";
				}
                if (!empty($field['is_search'])){
                    $custom_attributes['class'] .=' search';
                }
				echo wp_kses( sprintf( '<select %s>', self::implode_html_attributes( $custom_attributes ) ), self::filter_allowed_html() );
				if ( is_array( $select_options ) && count( $select_options ) ) {
					foreach ( $select_options as $k => $v ) {
						$selected = $multiple ? in_array( $k, $value ) : ( $k == $value );
						echo wp_kses( sprintf( '<option value="%s" %s>%s</option>',
							$k, $selected ? 'selected' : '', $v ), self::filter_allowed_html() );
					}
				}
				printf( '</select>' );
				break;
			case 'select2':
				$select_options = [];
				if ( ! empty( $value ) && is_array( $value ) ) {
					switch ($custom_attributes['data-type_select2'] ?? ''){
						case 'category':
						case 'tag':
							foreach ( $value as $item ) {
								$category = get_term( $item );
								if ( $category ) {
									$select_options[$item] = $category->name;
								}
							}
							break;
					}
				}
				$multiple = $field['multiple'] ?? '';
				unset($custom_attributes['type']);
				unset($custom_attributes['value']);
				if ($multiple){
					if (isset($custom_attributes['name'])) {
						$custom_attributes['name'] = $name . '[]';
					}
					$custom_attributes['multiple']= "multiple";
				}
				$custom_attributes['class'] .= ' edds-search-select2';
				echo wp_kses(sprintf('<select %s>', wc_implode_html_attributes( $custom_attributes)),self::filter_allowed_html());
				if (is_array($select_options) && count($select_options)){
					foreach ($select_options as $k => $v){
						printf( '<option value="%s" selected>%s</option>', esc_attr( $k ), wp_kses_post( $v ) );
					}
				}
				printf('</select>');
				break;
			case 'textarea':
				unset( $custom_attributes['type'] );
				unset( $custom_attributes['value'] );
				echo wp_kses( sprintf( '<textarea %s>%s</textarea>', self::implode_html_attributes( $custom_attributes ), $value ), self::filter_allowed_html() );
				break;
			default:
				if ( $type ) {
					echo wp_kses( sprintf( '<input %s>', self::implode_html_attributes( $custom_attributes ) ), self::filter_allowed_html() );
				}
		}
		if ( ! empty( $field['input_label'] ) ) {
			if ( ! empty( $input_label_type ) && $input_label_type === 'right' ) {
				printf( '<div class="%s">%s</div>', esc_attr( $field['input_label']['label_class'] ?? 'vi-ui label' ), wp_kses_post( $field['input_label']['label'] ?? '' ) );
			}
			printf( '</div>' );
		}
	}
	public static function villatheme_render_table_field( $options ) {
		if ( ! is_array( $options ) || empty( $options ) ) {
			return;
		}
		if ( ! empty( $options['html'] ) ) {
			echo wp_kses( $options['html'], self::filter_allowed_html() );
			return;
		}
		if ( isset( $options['section_start'] ) ) {
			if ( ! empty( $options['section_start']['accordion'] ) ) {
				echo wp_kses( sprintf( '<div class="vi-ui styled fluid accordion%s">
                                            <div class="title%s">
                                                <i class="dropdown icon"> </i>
                                                %s
                                            </div>
                                        <div class="content%s">',
					! empty( $options['section_start']['class'] ) ? " {$options['section_start']['class']}" : '',
					! empty( $options['section_start']['active'] ) ? " active" : '',
					$options['section_start']['title'] ?? '',
					! empty( $options['section_start']['active'] ) ? " active" : ''
				),
					self::filter_allowed_html() );
			}
			if ( empty( $options['fields_html'] ) ) {
				echo wp_kses_post( '<table class="form-table">' );
			}
		}
		if ( ! empty( $options['fields_html'] ) ) {
			echo wp_kses( $options['fields_html'], self::filter_allowed_html() );
		} else {
			$fields = $options['fields'] ?? '';
			if ( is_array( $fields ) && count( $fields ) ) {
				foreach ( $fields as $key => $param ) {
					$type = $param['type'] ?? '';
					$name = $param['name'] ?? $key;
					if ( ! $name ) {
						continue;
					}
					if ( ! empty( $param['prefix'] ) ) {
						$id = "edds-{$param['prefix']}-{$name}";
					} else {
						$id = "edds-{$name}";
					}
					if ( empty( $param['not_wrap_html'] ) ) {
						if ( ! empty( $param['wrap_class'] ) ) {
							printf( '<tr class="%s"><th><label for="%s">%s</label></th><td>',
								esc_attr( $param['wrap_class'] ), esc_attr( $type === 'checkbox' ? $id . '-' . $type : $id ), wp_kses_post( $param['title'] ?? '' ) );
						} else {
							printf( '<tr><th><label for="%s">%s</label></th><td>', esc_attr( $type === 'checkbox' ? $id . '-' . $type : $id ), wp_kses_post( $param['title'] ?? '' ) );
						}
					}
					do_action( 'edds_before_option_field', $name, $param );
					self::villatheme_render_field( $name, $param );
					if ( ! empty( $param['custom_desc'] ) ) {
						echo wp_kses_post( $param['custom_desc'] );
					}
					if ( ! empty( $param['desc'] ) ) {
						printf( '<p class="description">%s</p>', wp_kses_post( $param['desc'] ) );
					}
					if (!empty($param['after_desc'])){
						echo wp_kses( $param['after_desc'],self::filter_allowed_html());
					}
					do_action( 'edds_after_option_field', $name, $param );
					if ( empty( $param['not_wrap_html'] ) ) {
						echo wp_kses_post( '</td></tr>' );
					}
				}
			}
		}
		if ( isset( $options['section_end'] ) ) {
			if ( empty( $options['fields_html'] ) ) {
				echo wp_kses_post( '</table>' );
			}
			if ( ! empty( $options['section_end']['accordion'] ) ) {
				echo wp_kses_post( '</div></div>' );
			}
		}
	}
	/**
	 * @return bool
	 */
	public static function get_disable_wp_cron() {
		return defined( 'DISABLE_WP_CRON' ) && DISABLE_WP_CRON === true;
	}
	public static function get_product_status_options() {
		return array(
			'publish' => esc_html__( 'Publish', 'edds-ecommerce-dropshipping-suite' ),
			'pending' => esc_html__( 'Pending', 'edds-ecommerce-dropshipping-suite' ),
			'draft'   => esc_html__( 'Draft', 'edds-ecommerce-dropshipping-suite'),
		);
	}
	public static function get_product_tags() {
		$tags = get_terms( [
			'taxonomy'   => 'product_tag',
			'orderby'    => 'name',
			'order'      => 'ASC',
			'hide_empty' => false
		] );

		return wp_list_pluck( $tags, 'name', 'term_id' );
	}
	public static function get_product_categories() {
		$categories = get_categories( array( 'taxonomy' => 'product_cat', 'hide_empty' => false ) );

		return self::build_dropdown_categories_tree( $categories );
	}
	private static function build_dropdown_categories_tree( $all_cats, $parent_cat = 0, $level = 1 ) {
		foreach ( $all_cats as $cat ) {
			if ( $cat->parent == $parent_cat ) {
				$prefix               = str_repeat( '&nbsp;-&nbsp;', $level - 1 );
				$res[ $cat->term_id ] = $prefix . $cat->name . " ({$cat->count})";
				$child_cats           = self::build_dropdown_categories_tree( $all_cats, $cat->term_id, $level + 1 );
				if ( $child_cats ) {
					$res += $child_cats;
				}
			}
		}
		return $res ?? [];
	}

	public static function get_catalog_visibility_options() {
		return wc_get_product_visibility_options();
	}
	public static function get_shipping_class_options() {
		$shipping_classes = get_terms(
			array(
				'taxonomy'   => 'product_shipping_class',
				'orderby'    => 'name',
				'order'      => 'ASC',
				'hide_empty' => false
			)
		);
		return wp_list_pluck( $shipping_classes, 'name', 'term_id' );
	}
	/**
	 * @param string $sku
	 *
	 * @return bool
	 */
	public static function sku_exists( $sku = '' ) {
		$sku_exists = false;

		if ( $sku ) {
			$id_from_sku = wc_get_product_id_by_sku( $sku );
			$product     = $id_from_sku ? wc_get_product( $id_from_sku ) : false;
			$sku_exists  = $product && 'importing' !== $product->get_status();
		}

		return $sku_exists;
	}
	public static function create_ajax_nonce( $type = 'admin_ajax' ) {
		return apply_filters( 'edds_ajax_nonce', wp_create_nonce( 'edds_' . $type ) );
	}
	public static function json_decode( $json, $assoc = true, $depth = 512, $options = 2 ) {
		if ( is_array( $json ) ) {
			return $json;
		}
		if ( function_exists( 'mb_convert_encoding' ) ) {
			$json = mb_convert_encoding( $json, 'UTF-8', 'UTF-8' );
		}

		return json_decode( is_string( $json ) ? $json : '{}', $assoc, $depth, $options );
	}
	public static function strtolower( $string ) {
		return function_exists( 'mb_strtolower' ) ? mb_strtolower( $string ) : strtolower( $string );
	}
	/**
	 * @param $name
	 *
	 * @return string
	 */
	public static function sanitize_taxonomy_name( $name ) {
		return rawurlencode( self::strtolower( rawurlencode( wc_sanitize_taxonomy_name( $name ) ) ) );
	}
	/**
	 * @param $string_number
	 *
	 * @return float
	 */
	public static function string_to_float( $string_number ) {
		return floatval( str_replace( ',', '', $string_number ) );
	}
	/**
	 * @param $slug
	 *
	 * @return string
	 */
	public static function get_attribute_name_by_slug( $slug ) {
		return ucwords( str_replace( '-', ' ', $slug ) );
	}
	/**
	 * @param $price
	 *
	 * @return float|int
	 */
	public function process_exchange_price( $price, $currency, $rate = null ) {
		if ( ! $price ) {
			return $price;
		}
		$price = $this->get_price_with_temu_decimals($price,$currency);

		if ($rate === null) {
			$import_currency_rate = self::get_instance()->get_params( 'import_currency_rate' );
			$rate = $import_currency_rate[$currency] ?? 0 ;
		}
		if ( $rate ) {
			$price = floatval($price) * floatval( $rate);
		}
		return $price;
	}
	/**
	 * @param $price
	 * @param bool $is_sale_price
	 * @param bool $product_id
	 *
	 * @return float|int|mixed|void
	 */
	public function process_price( $price, $is_sale_price = false, $product_id = false ) {
		if ( ! $price ) {
			return $price;
		}
		$settings        = self::get_instance();
		$price_default   = $settings->get_params( 'price_default' );
		$price_from      = $settings->get_params( 'price_from' );
		$price_to        = $settings->get_params( 'price_to' );
		$plus_value_type = $settings->get_params( 'plus_value_type' );
		$plus_value      = $settings->get_params( 'plus_value' );
		$plus_sale_value = $settings->get_params( 'plus_sale_value' );
		if ( $product_id ) {
			$product = wc_get_product( $product_id );
			if ( $product ) {
				$custom_rules = $settings->get_params( 'update_product_custom_rules' );
				if ( is_array( $custom_rules ) && count( $custom_rules ) ) {
					if ( $product->is_type( 'variation' ) ) {
						$product_id         = $product->get_parent_id();
						$parent             = wc_get_product( $product_id );
						$product_categories = $parent->get_category_ids();
					} else {
						$product_categories = $product->get_category_ids();
					}
					foreach ( $custom_rules as $custom_rule ) {
						if ( $custom_rule['products'] && ! in_array( $product_id, $custom_rule['products'] ) ) {
							continue;
						}
						if ( $custom_rule['excl_products'] && in_array( $product_id, $custom_rule['excl_products'] ) ) {
							continue;
						}
						if ( $custom_rule['categories'] && ! count( array_intersect( $custom_rule['categories'], $product_categories ) ) ) {
							continue;
						}
						if ( $custom_rule['excl_categories'] && count( array_intersect( $custom_rule['excl_categories'], $product_categories ) ) ) {
							continue;
						}
						$price_from      = $custom_rule['price_from'];
						$price_default   = $custom_rule['price_default'];
						$price_to        = $custom_rule['price_to'];
						$plus_value      = $custom_rule['plus_value'];
						$plus_sale_value = $custom_rule['plus_sale_value'];
						$plus_value_type = $custom_rule['plus_value_type'];
						break;
					}
				}
			}
		}
		$original_price = $price;
		if ( $is_sale_price ) {
			$level_count = count( $price_from );
			if ( $level_count > 0 ) {
				/*adjust price rules */
				if ( ! is_array( $price_to ) || count( $price_to ) !== $level_count ) {
					if ( $level_count > 1 ) {
						$price_to   = array_values( array_slice( $price_from, 1 ) );
						$price_to[] = '';
					} else {
						$price_to = array( '' );
					}
				}
				$match = false;
				for ( $i = 0; $i < $level_count; $i ++ ) {
					if ( $price >= $price_from[ $i ] && ( $price_to[ $i ] === '' || $price <= $price_to[ $i ] ) ) {
						$match = $i;
						break;
					}
				}
				if ( $match !== false ) {
					if ( $plus_sale_value[ $match ] < 0 ) {
						$price = '';
					} else {
						$price = self::calculate_price_base_on_type( $price, $plus_sale_value[ $match ], $plus_value_type[ $match ] );
					}
				} else {
					$plus_sale_value_default = isset( $price_default['plus_sale_value'] ) ? $price_default['plus_sale_value'] : 1;
					if ( $plus_sale_value_default < 0 ) {
						$price = '';
					} else {
						$price = self::calculate_price_base_on_type( $price, $plus_sale_value_default, isset( $price_default['plus_value_type'] ) ? $price_default['plus_value_type'] : 'multiply' );
					}
				}
			}
		} else {
			$level_count = count( $price_from );
			if ( $level_count > 0 ) {
				/*adjust price rules*/
				if ( ! is_array( $price_to ) || count( $price_to ) !== $level_count ) {
					if ( $level_count > 1 ) {
						$price_to   = array_values( array_slice( $price_from, 1 ) );
						$price_to[] = '';
					} else {
						$price_to = array( '' );
					}
				}
				$match = false;
				for ( $i = 0; $i < $level_count; $i ++ ) {
					if ( $price >= $price_from[ $i ] && ( $price_to[ $i ] === '' || $price <= $price_to[ $i ] ) ) {
						$match = $i;
						break;
					}
				}
				if ( $match !== false ) {
					$price = self::calculate_price_base_on_type( $price, $plus_value[ $match ], $plus_value_type[ $match ] );
				} else {
					$price = self::calculate_price_base_on_type( $price, isset( $price_default['plus_value'] ) ? $price_default['plus_value'] : 2, isset( $price_default['plus_value_type'] ) ? $price_default['plus_value_type'] : 'multiply' );
				}
			}
		}
		return apply_filters( 'villatheme_'.self::$prefix.'_processed_price', is_numeric($price)? round( floatval($price), wc_get_price_decimals() ): $price, $is_sale_price, $original_price );
	}
	/**
	 * @param $price
	 * @param $value
	 * @param $type
	 *
	 * @return float|int
	 */
	protected static function calculate_price_base_on_type( $price, $value, $type ) {
		$match_value = floatval( $value );
		switch ( $type ) {
			case 'fixed':
				$price = $price + $match_value;
				break;
			case 'percent':
				$price = $price * ( 1 + $match_value / 100 );
				break;
			case 'multiply':
				$price = $price * $match_value;
				break;
			default:
				$price = $match_value;
		}

		return $price;
	}
	/**
	 * @param $image_id
	 * @param bool $count
	 * @param bool $multiple
	 *
	 * @return array|bool|object|string|null
	 */
	public static function get_id_by_image_id( $image_id, $count = false, $multiple = false ) {
		if ( $image_id ) {
            $args = [
	            'post_type'      => 'attachment',
	            'order'          => 'DESC',
	            'fields'         => 'ids',
	            'post_status'          =>'any',
	            'posts_per_page' => $count  || $multiple ? -1 : 1,
	            'meta_query' => [// phpcs:ignore WordPress.DB.SlowDBQuery.slow_db_query_meta_query
		            'relation' => 'and',
		            [
			            'key'     => '_' . self::$prefix . '_image_id',
			            'compare' => '=',
			            'value' => $image_id,
		            ]
	            ],
            ];
            $post_ids = get_posts($args);
            if ($count){
                return count($post_ids);
            }else{
                return $multiple? $post_ids : ($post_ids[0]??'');
            }
		} else {
			return false;
		}
	}
	public static function upload_image( $url, $post_parent = 0, $exclude = array(), $post_title = '', $desc = null ) {
		preg_match( '/[^\?]+\.(jpg|JPG|jpeg|JPEG|jpe|JPE|gif|GIF|png|PNG|webp|WEBP)/', $url, $matches );
		if ( is_array( $matches ) && count( $matches ) ) {
			if ( ! in_array( self::strtolower( $matches[1] ), $exclude ) ) {
				add_filter( 'big_image_size_threshold', '__return_false' );
				//add product image:
				if ( ! function_exists( 'media_handle_upload' ) ) {
					require_once( ABSPATH . "wp-admin" . '/includes/image.php' );
					require_once( ABSPATH . "wp-admin" . '/includes/file.php' );
					require_once( ABSPATH . "wp-admin" . '/includes/media.php' );
				}
				// Download file to temp location
				$tmp                    = download_url( $url );
				$img_name = trim(wp_parse_url($url)['path']??'','/');
				if ($img_name){
					$img_name = str_replace('/','-',$img_name);
					$img_name = substr($img_name,0,strpos($img_name,$matches[1]));
				}
				if (!$img_name){
					$img_name = basename( $matches[0] );
					$img_name = substr($img_name,0,strpos($img_name,$matches[1])).time();
				}
				$file_array['name']     = apply_filters( 'edds_upload_image_file_name', $img_name.$matches[1], $post_parent, $post_title );
				$file_array['tmp_name'] = $tmp;

				// If error storing temporarily, unlink
				if ( is_wp_error( $tmp ) ) {
					return $tmp;
				}
				$args = array();
				if ( $post_parent ) {
					$args['post_parent'] = $post_parent;
				}
				if ( $post_title ) {
					$args['post_title'] = $post_title;
				}
				//use media_handle_sideload to upload img:
				$thumbid = media_handle_sideload( $file_array, '', $desc, $args );
				// If error storing permanently, unlink
				if ( is_wp_error( $thumbid ) ) {
					@unlink( $file_array['tmp_name'] );// @codingStandardsIgnoreLine.
				}

				return $thumbid;
			} else {
				return new WP_Error( 'edds_file_type_not_permitted', esc_html__( 'File type is not permitted', 'edds-ecommerce-dropshipping-suite' ) );
			}
		} else {
			return new WP_Error( 'edds_file_type_not_permitted', esc_html__( 'Can not detect file type', 'edds-ecommerce-dropshipping-suite' ) );
		}
	}

	public static function download_image( &$image_id, $url, $post_parent = 0, $exclude = array(), $post_title = '', $desc = null ) {
		$settings = self::get_instance();

		if ( $settings->get_params( 'use_external_image' ) && class_exists( 'EXMAGE_WP_IMAGE_LINKS' ) ) {
			$external_image = EXMAGE_WP_IMAGE_LINKS::add_image( $url, $image_id, $post_parent );
			$thumb_id       = $external_image['id'] ? $external_image['id'] : new \WP_Error( 'exmage_image_error', $external_image['message'] );
		} else {
			$new_url   = $url;
			$parse_url = wp_parse_url( $new_url );
			$scheme    = empty( $parse_url['scheme'] ) ? 'http' : $parse_url['scheme'];
			$image_id  = "{$parse_url['host']}{$parse_url['path']}";
			$new_url   = "{$scheme}://{$image_id}";

			preg_match( '/[^\?]+\.(jpg|JPG|jpeg|JPEG|jpe|JPE|gif|GIF|png|PNG)/', $new_url, $matches );
			if ( ! is_array( $matches ) || ! count( $matches ) ) {
				preg_match( '/[^\?]+\.(jpg|JPG|jpeg|JPEG|jpe|JPE|gif|GIF|png|PNG)/', $url, $matches );
				if ( is_array( $matches ) && count( $matches ) ) {
					$new_url  .= "?{$matches[0]}";
					$image_id .= "?{$matches[0]}";
				} elseif ( ! empty( $parse_url['query'] ) ) {
					$new_url .= '?' . $parse_url['query'];
				}
			} elseif ( ! empty( $parse_url['query'] ) ) {
				$new_url .= '?' . $parse_url['query'];
			}

			$thumb_id = self::get_id_by_image_id( $image_id );
			if ( ! $thumb_id ) {
				$thumb_id = self::upload_image( $new_url, $post_parent, $exclude, $post_title, $desc );
				if ( ! is_wp_error( $thumb_id ) ) {
					update_post_meta( $thumb_id, '_'.self::$prefix.'_image_id', $image_id );
				}
			} elseif ( $post_parent ) {
				global $wpdb;
				$wpdb->query( $wpdb->prepare( "UPDATE %i set post_parent=%s WHERE ID=%s AND post_parent = 0 LIMIT 1", [ $wpdb->posts,$post_parent, $thumb_id ] ) );// phpcs:ignore WordPress.DB.DirectDatabaseQuery.DirectQuery, WordPress.DB.DirectDatabaseQuery.NoCaching
			}
		}

		return $thumb_id;
	}
	public static function villatheme_set_time_limit() {
		ActionScheduler_Compatibility::raise_memory_limit();
		wc_set_time_limit();
	}

	public static function enqueue_style( $handles = array(), $srcs = array(), $is_suffix = array(), $des = array(), $type = 'enqueue' ) {
		if ( empty( $handles ) || empty( $srcs ) ) {
			return;
		}
		$action = $type === 'enqueue' ? 'wp_enqueue_style' : 'wp_register_style';
		$suffix = WP_DEBUG ? '' : '.min';
		foreach ( $handles as $i => $handle ) {
			if ( ! $handle || empty( $srcs[ $i ] ) ) {
				continue;
			}
			$suffix_t = ! empty( $is_suffix[ $i ] ) ? '.min' : $suffix;
			$action( $handle, EDDS_CSS . $srcs[ $i ] . $suffix_t . '.css', ! empty( $des[ $i ] ) ? $des[ $i ] : array(), EDDS_VERSION );
		}
	}

	public static function enqueue_script( $handles = array(), $srcs = array(), $is_suffix = array(), $des = array(), $type = 'enqueue', $in_footer = false ) {
		if ( empty( $handles ) || empty( $srcs ) ) {
			return;
		}
		$action = $type === 'register' ? 'wp_register_script' : 'wp_enqueue_script';
		$suffix = WP_DEBUG ? '' : '.min';
		foreach ( $handles as $i => $handle ) {
			if ( ! $handle || empty( $srcs[ $i ] ) ) {
				continue;
			}
			$suffix_t = ! empty( $is_suffix[ $i ] ) ? '.min' : $suffix;
			$action( $handle, EDDS_JS . $srcs[ $i ] . $suffix_t . '.js', ! empty( $des[ $i ] ) ? $des[ $i ] : array( 'jquery' ),
				EDDS_VERSION, $in_footer );
		}
	}
}