<?php
defined( 'ABSPATH' ) || exit;
class EDDS_Post_Query extends WP_Query {
	public function get_posts() {
		global $wpdb;
		add_filter( 'posts_join', [$this,'custom_posts_join'], 10, 2 );
		$wp_posts = $wpdb->posts;
		$wp_postmeta = $wpdb->postmeta;
		$wpdb->posts = $wpdb->edds_posts;
		$wpdb->postmeta = $wpdb->edds_postmeta;
		$posts = parent::get_posts();
		$wpdb->posts = $wp_posts;
		$wpdb->postmeta = $wp_postmeta;
		remove_filter('posts_join', [$this,'custom_posts_join']);
		return $posts;
	}
	public function custom_posts_join($join, $query){
		if ( !empty( $query->query_vars['edds_query'] ) ) {
			$join = str_replace('edds_postmeta.post_id','edds_postmeta.edds_post_id', $join);
		}
		return $join;
	}
}