=== CodeCareBD - Payment Gateway for WooCommerce ===
Contributors: devshakil, codecarebd
Requires at least: 6.3
Requires PHP: 7.3
Tested up to: 6.7.1
Stable tag: 0.8
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Tags: bKash, nagad, payoneer, rocket, woocommerce paymeny gateway

CodeCareBD - Payment Gateway plugin integrates bKash, Nagad, Rocket, and Payoneer Payment Gateways with WooCommerce. Lightweight and easy to use.

== How to use ==

[youtube https://www.youtube.com/watch?v=yrK5dhQpX68]

== Please note: ==

- This is a WooCommerce plugin, requiring WooCommerce activation.
- You need accounts with bKash, Nagad, Rocket, or Payoneer to receive payments.

For suggestions and support, contact us [here](https://codecarebd.com/contact).

== Screenshots ==

1. Payment Settings (bKash).
2. Payment Settings (Rocket).
3. Payment Settings (Nagad).
4. Payment Settings (Payoneer).
5. Checkout Page.

== Changelog ==

= 9/21/2022 =
* First release.

= 8/24/2023 =
* Small Fixes.

= 09/01/2023 =
* Bug Fixes.

= 13/02/2024 =
* Updated for new WooCommerce version.
* Fixed validation issues.

= 16/02/2024 =
* Fixed issues on Single Order Page.

= 25/12/2024 =
* Validation Error Fix.
